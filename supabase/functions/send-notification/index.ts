import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[SEND-NOTIFICATION] ${step}${detailsStr}`);
};

interface NotificationRequest {
  user_id: string;
  type: string;
  title: string;
  message: string;
  send_email?: boolean;
  email_subject?: string;
  email_template?: string;
}

const getEmailTemplate = (type: string, data: any) => {
  const templates = {
    application_submitted: {
      subject: "Application Submitted Successfully",
      html: `
        <h1>Your Medical Cannabis License Application Has Been Submitted</h1>
        <p>Dear ${data.user_name},</p>
        <p>Your application has been successfully submitted and is now under review.</p>
        <p><strong>Application ID:</strong> ${data.application_id}</p>
        <p>You will receive updates on your application status via email and in your dashboard.</p>
        <p>Best regards,<br>MedCannabis Thailand Team</p>
      `
    },
    consultation_scheduled: {
      subject: "Medical Consultation Scheduled",
      html: `
        <h1>Your Medical Consultation Has Been Scheduled</h1>
        <p>Dear ${data.user_name},</p>
        <p>Your medical consultation has been scheduled with Dr. ${data.doctor_name}.</p>
        <p><strong>Date & Time:</strong> ${data.consultation_date}</p>
        <p><strong>Meeting Link:</strong> <a href="${data.meeting_link}">Join Consultation</a></p>
        <p>Please be prepared with your medical history and any relevant documents.</p>
        <p>Best regards,<br>MedCannabis Thailand Team</p>
      `
    },
    application_approved: {
      subject: "License Application Approved",
      html: `
        <h1>Congratulations! Your License Has Been Approved</h1>
        <p>Dear ${data.user_name},</p>
        <p>We're pleased to inform you that your medical cannabis license application has been approved.</p>
        <p><strong>License Number:</strong> ${data.license_number}</p>
        <p><strong>Valid Until:</strong> ${data.expires_at}</p>
        <p>You can download your digital license from your dashboard.</p>
        <p>Best regards,<br>MedCannabis Thailand Team</p>
      `
    },
    application_rejected: {
      subject: "License Application Update",
      html: `
        <h1>Application Review Update</h1>
        <p>Dear ${data.user_name},</p>
        <p>After careful review, we were unable to approve your current application.</p>
        <p><strong>Reason:</strong> ${data.rejection_reason}</p>
        <p>You may submit a new application with additional documentation if needed.</p>
        <p>Best regards,<br>MedCannabis Thailand Team</p>
      `
    },
    payment_successful: {
      subject: "Payment Confirmed",
      html: `
        <h1>Payment Received Successfully</h1>
        <p>Dear ${data.user_name},</p>
        <p>Your payment has been processed successfully.</p>
        <p><strong>Amount:</strong> ${data.amount} ${data.currency}</p>
        <p><strong>Application ID:</strong> ${data.application_id}</p>
        <p>Your application will now proceed to the next stage of review.</p>
        <p>Best regards,<br>MedCannabis Thailand Team</p>
      `
    }
  };

  return templates[type as keyof typeof templates] || {
    subject: data.title || "MedCannabis Notification",
    html: `
      <h1>${data.title}</h1>
      <p>${data.message}</p>
      <p>Best regards,<br>MedCannabis Thailand Team</p>
    `
  };
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const requestData: NotificationRequest = await req.json();
    const { user_id, type, title, message, send_email = true, email_subject, email_template } = requestData;

    if (!user_id || !type || !title || !message) {
      throw new Error("Missing required fields: user_id, type, title, message");
    }

    logStep("Notification request", { user_id, type, title });

    // Create Supabase client with service role
    const supabaseService = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Get user profile for email
    const { data: profile, error: profileError } = await supabaseService
      .from("profiles")
      .select("email, full_name")
      .eq("user_id", user_id)
      .single();

    if (profileError) {
      throw new Error(`Failed to fetch user profile: ${profileError.message}`);
    }

    logStep("User profile fetched", { email: profile.email, name: profile.full_name });

    // Create notification record
    const { error: notificationError } = await supabaseService
      .from("notifications")
      .insert({
        user_id,
        type,
        title,
        message,
      });

    if (notificationError) {
      console.error("Failed to create notification record:", notificationError);
    } else {
      logStep("Notification record created");
    }

    // Send email if requested
    if (send_email && profile.email) {
      try {
        const templateData = {
          user_name: profile.full_name || "User",
          title,
          message,
          ...requestData // Include any additional data from the request
        };

        const emailContent = getEmailTemplate(type, templateData);

        const emailResponse = await resend.emails.send({
          from: "MedCannabis <<EMAIL>>",
          to: [profile.email],
          subject: email_subject || emailContent.subject,
          html: email_template || emailContent.html,
        });

        logStep("Email sent successfully", { messageId: emailResponse.data?.id });
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        logStep("Email sending failed", { error: emailError });
      }
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: "Notification sent successfully" 
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR in send-notification", { message: errorMessage });
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});