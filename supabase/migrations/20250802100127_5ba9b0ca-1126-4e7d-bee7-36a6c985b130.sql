-- Create missing doctor record for existing user <PERSON> Kerr
INSERT INTO public.doctors (
  user_id,
  medical_license_number,
  specialization,
  years_of_experience,
  status
) VALUES (
  '1cc189c4-4ced-497c-8e54-776a72359358',
  'TEMP_LICENSE',
  'General Practice',
  0,
  'pending_review'
);

-- Create documents storage bucket for doctor document uploads
INSERT INTO storage.buckets (id, name, public) 
VALUES ('documents', 'documents', false);

-- Create RLS policies for documents bucket
CREATE POLICY "Doctors can upload their own documents" 
ON storage.objects 
FOR INSERT 
WITH CHECK (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Doctors can view their own documents" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Admins can view all documents" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'documents' AND EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));