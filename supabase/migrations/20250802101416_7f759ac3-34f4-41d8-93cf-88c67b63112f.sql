-- Add foreign key constraint between doctors and profiles tables
ALTER TABLE public.doctors 
ADD CONSTRAINT fk_doctors_user_id 
FOREIGN KEY (user_id) REFERENCES public.profiles(user_id) 
ON DELETE CASCADE;

-- Add missing RLS policies for admins to view all doctor data
CREATE POLICY "Ad<PERSON> can view all doctors" 
ON public.doctors 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));

-- Add missing RLS policies for admins to update all doctor data  
CREATE POLICY "Admins can update all doctors" 
ON public.doctors 
FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));

-- Add missing RLS policies for admins to view all profiles
CREATE POLICY "Ad<PERSON> can view all profiles" 
ON public.profiles 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.profiles p2
  WHERE p2.user_id = auth.uid() AND p2.role = 'admin'
));