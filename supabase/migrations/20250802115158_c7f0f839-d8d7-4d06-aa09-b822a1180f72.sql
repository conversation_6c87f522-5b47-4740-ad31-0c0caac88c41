-- Drop the problematic RLS policies that cause infinite recursion
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all doctors" ON public.doctors;
DROP POLICY IF EXISTS "Ad<PERSON> can update all doctors" ON public.doctors;

-- Create a security definer function to get current user role
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT AS $$
  SELECT role::text FROM public.profiles WHERE user_id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Recreate the RLS policies using the security definer function
CREATE POLICY "<PERSON><PERSON> can view all profiles" 
ON public.profiles 
FOR SELECT 
USING (public.get_current_user_role() = 'admin');

CREATE POLICY "Ad<PERSON> can view all doctors" 
ON public.doctors 
FOR SELECT 
USING (public.get_current_user_role() = 'admin');

CREATE POLICY "Ad<PERSON> can update all doctors" 
ON public.doctors 
FOR UPDATE 
USING (public.get_current_user_role() = 'admin');