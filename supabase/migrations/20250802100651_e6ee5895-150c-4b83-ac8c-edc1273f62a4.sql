-- Create an admin user account
-- First, let's check if we already have an admin user, if not create one
DO $$
BEGIN
  -- Check if admin user exists, if not insert
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE role = 'admin') THEN
    -- Insert a default admin user (you can change the email later)
    INSERT INTO auth.users (
      id,
      email,
      email_confirmed_at,
      created_at,
      updated_at,
      raw_user_meta_data
    ) VALUES (
      '********-0000-0000-0000-************',
      '<EMAIL>',
      now(),
      now(),
      now(),
      '{"full_name": "Platform Admin", "role": "admin"}'::jsonb
    ) ON CONFLICT (id) DO NOTHING;
    
    -- Insert admin profile
    INSERT INTO public.profiles (
      user_id,
      email,
      full_name,
      role,
      created_at,
      updated_at
    ) VALUES (
      '********-0000-0000-0000-************',
      '<EMAIL>',
      'Platform Admin',
      'admin',
      now(),
      now()
    ) ON CONFLICT (user_id) DO NOTHING;
  END IF;
END $$;

-- Create audit logs table for admin actions
CREATE TABLE IF NOT EXISTS public.admin_audit_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id uuid NOT NULL REFERENCES public.profiles(user_id),
  action_type text NOT NULL,
  target_type text NOT NULL,
  target_id uuid,
  details jsonb DEFAULT '{}',
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now()
);

-- Enable RLS on audit logs
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Admin RLS policies for audit logs
CREATE POLICY "Admins can view all audit logs" 
ON public.admin_audit_logs 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));

CREATE POLICY "Admins can insert audit logs" 
ON public.admin_audit_logs 
FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));

-- Create admin notifications table
CREATE TABLE IF NOT EXISTS public.admin_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  message text NOT NULL,
  type text NOT NULL DEFAULT 'info',
  target_user_id uuid REFERENCES public.profiles(user_id),
  target_role user_role,
  is_read boolean DEFAULT false,
  created_by uuid NOT NULL REFERENCES public.profiles(user_id),
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone
);

-- Enable RLS on admin notifications
ALTER TABLE public.admin_notifications ENABLE ROW LEVEL SECURITY;

-- Admin RLS policies for notifications
CREATE POLICY "Users can view their own notifications" 
ON public.admin_notifications 
FOR SELECT 
USING (
  target_user_id = auth.uid() OR 
  (target_role IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() AND role = target_role
  ))
);

CREATE POLICY "Admins can manage all notifications" 
ON public.admin_notifications 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM public.profiles 
  WHERE user_id = auth.uid() AND role = 'admin'
));