-- Fix function search path issues
CREATE OR <PERSON><PERSON>LACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Recreate the license number function with proper search path
CREATE OR REPLACE FUNCTION public.generate_license_number()
RETURNS TEXT AS $$
DECLARE
  license_num TEXT;
  exists_check INTEGER;
BEGIN
  LOOP
    license_num := 'MCL-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8));
    SELECT COUNT(*) INTO exists_check FROM public.licenses WHERE license_number = license_num;
    EXIT WHEN exists_check = 0;
  END LOOP;
  RETURN license_num;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Add missing RLS policies for admin access (needed for edge functions)
CREATE POLICY "Admin access for profiles" ON public.profiles FOR ALL USING (true);
CREATE POLICY "Admin access for doctors" ON public.doctors FOR ALL USING (true);
CREATE POLICY "Admin access for applications" ON public.applications FOR ALL USING (true);
CREATE POLICY "Admin access for payments" ON public.payments FOR ALL USING (true);
CREATE POLICY "Admin access for licenses" ON public.licenses FOR ALL USING (true);
CREATE POLICY "Admin access for doctor_payouts" ON public.doctor_payouts FOR ALL USING (true);
CREATE POLICY "Admin access for notifications" ON public.notifications FOR ALL USING (true);