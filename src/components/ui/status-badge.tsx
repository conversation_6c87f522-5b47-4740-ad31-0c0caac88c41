import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const statusBadgeVariants = cva(
  "font-medium",
  {
    variants: {
      status: {
        draft: "bg-muted text-muted-foreground",
        pending: "bg-warning/20 text-warning-foreground border-warning/30",
        under_review: "bg-info/20 text-info-foreground border-info/30",
        consultation_scheduled: "bg-primary/20 text-primary border-primary/30",
        consultation_completed: "bg-primary/30 text-primary border-primary/40",
        approved: "bg-success/20 text-success-foreground border-success/30",
        rejected: "bg-destructive/20 text-destructive-foreground border-destructive/30",
        active: "bg-success/20 text-success-foreground border-success/30",
        expired: "bg-muted text-muted-foreground",
        paid: "bg-success/20 text-success-foreground border-success/30",
        failed: "bg-destructive/20 text-destructive-foreground border-destructive/30"
      }
    },
    defaultVariants: {
      status: "draft"
    }
  }
);

interface StatusBadgeProps {
  status: string;
  className?: string;
}

const statusLabels: Record<string, string> = {
  draft: "Draft",
  pending: "Pending Review",
  under_review: "Under Review",
  consultation_scheduled: "Consultation Scheduled",
  consultation_completed: "Consultation Completed",
  approved: "Approved",
  rejected: "Rejected",
  active: "Active",
  expired: "Expired",
  paid: "Paid",
  failed: "Failed"
};

type StatusType = 'draft' | 'pending' | 'under_review' | 'consultation_scheduled' | 'consultation_completed' | 'approved' | 'rejected' | 'active' | 'expired' | 'paid' | 'failed';

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const normalizedStatus = status.toLowerCase().replace(/\s+/g, '_');
  const validStatus: StatusType = (normalizedStatus in statusLabels) ? normalizedStatus as StatusType : 'draft';
  
  return (
    <Badge 
      variant="outline"
      className={cn(statusBadgeVariants({ status: validStatus }), className)}
    >
      {statusLabels[normalizedStatus] || status}
    </Badge>
  );
};