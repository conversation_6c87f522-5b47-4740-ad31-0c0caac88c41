import React from 'react';
import { Link } from 'react-router-dom';
import { Leaf, Mail, Phone, MapPin } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-primary text-primary-foreground mt-auto">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <Leaf className="w-5 h-5 text-accent-foreground" />
              </div>
              <span className="text-xl font-bold">MedCannabis</span>
            </div>
            <p className="text-primary-foreground/80 text-sm">
              Professional medical cannabis licensing services in Thailand. 
              Safe, legal, and regulated access to medical cannabis.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/about" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/process" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  Licensing Process
                </Link>
              </li>
              <li>
                <Link to="/doctors" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  Find Doctors
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Services</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/apply" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  License Application
                </Link>
              </li>
              <li>
                <Link to="/consultation" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  Medical Consultation
                </Link>
              </li>
              <li>
                <Link to="/renewal" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  License Renewal
                </Link>
              </li>
              <li>
                <Link to="/support" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                  Customer Support
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Contact</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span className="text-primary-foreground/80">+66 2-123-4567</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span className="text-primary-foreground/80"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span className="text-primary-foreground/80">Bangkok, Thailand</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm text-primary-foreground/60">
          <p>&copy; 2024 MedCannabis Thailand. All rights reserved. | Licensed by Ministry of Public Health</p>
        </div>
      </div>
    </footer>
  );
};