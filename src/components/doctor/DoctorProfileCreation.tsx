import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface DoctorProfileCreationProps {
  userId: string;
  onProfileCreated: () => void;
}

export default function DoctorProfileCreation({ userId, onProfileCreated }: DoctorProfileCreationProps) {
  const [formData, setFormData] = useState({
    medicalLicenseNumber: '',
    specialization: '',
    yearsOfExperience: '',
    bio: '',
    clinicAddress: '',
  });
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [documents, setDocuments] = useState({
    medicalLicense: null as File | null,
    nationalId: null as File | null,
  });
  const { toast } = useToast();

  const specializations = [
    'General Practice',
    'Internal Medicine',
    'Cardiology',
    'Dermatology',
    'Endocrinology',
    'Gastroenterology',
    'Neurology',
    'Oncology',
    'Orthopedics',
    'Pediatrics',
    'Psychiatry',
    'Radiology',
    'Surgery',
    'Urology',
    'Other'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (type: 'medicalLicense' | 'nationalId', file: File) => {
    setDocuments(prev => ({ ...prev, [type]: file }));
  };

  const uploadDocument = async (file: File, folder: string) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${folder}/${userId}/${Date.now()}.${fileExt}`;

    const { data, error } = await supabase.storage
      .from('documents')
      .upload(fileName, file);

    if (error) throw error;
    return data.path;
  };

  const submitProfile = async () => {
    if (!formData.medicalLicenseNumber || !formData.specialization || !formData.yearsOfExperience) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setSubmitting(true);
    try {
      // Upload documents if provided
      let medicalLicensePath = null;
      let nationalIdPath = null;

      if (documents.medicalLicense) {
        medicalLicensePath = await uploadDocument(documents.medicalLicense, 'medical-licenses');
      }
      if (documents.nationalId) {
        nationalIdPath = await uploadDocument(documents.nationalId, 'national-ids');
      }

      // Create doctor profile
      const { error } = await supabase
        .from('doctors')
        .insert({
          user_id: userId,
          medical_license_number: formData.medicalLicenseNumber,
          specialization: formData.specialization,
          years_of_experience: parseInt(formData.yearsOfExperience),
          bio: formData.bio || null,
          clinic_address: formData.clinicAddress || null,
          status: 'pending_review'
        });

      if (error) throw error;

      toast({
        title: "Profile Created",
        description: "Your doctor profile has been submitted for review",
      });

      onProfileCreated();
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: "Error",
        description: "Failed to create profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Complete Your Doctor Profile</CardTitle>
        <CardDescription>
          Please provide your professional information to complete your application
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            All information will be reviewed by our medical team before approval.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="license">Medical License Number *</Label>
            <Input
              id="license"
              placeholder="Enter your medical license number"
              value={formData.medicalLicenseNumber}
              onChange={(e) => handleInputChange('medicalLicenseNumber', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="specialization">Specialization *</Label>
            <Select 
              value={formData.specialization} 
              onValueChange={(value) => handleInputChange('specialization', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select your specialization" />
              </SelectTrigger>
              <SelectContent>
                {specializations.map((spec) => (
                  <SelectItem key={spec} value={spec}>
                    {spec}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="experience">Years of Experience *</Label>
            <Input
              id="experience"
              type="number"
              min="0"
              max="50"
              placeholder="5"
              value={formData.yearsOfExperience}
              onChange={(e) => handleInputChange('yearsOfExperience', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="clinic">Clinic/Hospital Address</Label>
            <Input
              id="clinic"
              placeholder="Your clinic or hospital address"
              value={formData.clinicAddress}
              onChange={(e) => handleInputChange('clinicAddress', e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Professional Bio</Label>
          <Textarea
            id="bio"
            placeholder="Tell patients about your background, expertise, and approach to care..."
            value={formData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            rows={4}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Document Uploads</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Medical License Document</Label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                <input
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => e.target.files && handleFileUpload('medicalLicense', e.target.files[0])}
                  className="hidden"
                  id="medical-license-upload"
                />
                <label htmlFor="medical-license-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center text-center">
                    {documents.medicalLicense ? (
                      <>
                        <FileText className="w-8 h-8 text-green-600 mb-2" />
                        <p className="text-sm font-medium text-green-600">
                          {documents.medicalLicense.name}
                        </p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-8 h-8 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">
                          Upload medical license
                        </p>
                      </>
                    )}
                  </div>
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>National ID Document</Label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                <input
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => e.target.files && handleFileUpload('nationalId', e.target.files[0])}
                  className="hidden"
                  id="national-id-upload"
                />
                <label htmlFor="national-id-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center text-center">
                    {documents.nationalId ? (
                      <>
                        <FileText className="w-8 h-8 text-green-600 mb-2" />
                        <p className="text-sm font-medium text-green-600">
                          {documents.nationalId.name}
                        </p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-8 h-8 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">
                          Upload national ID
                        </p>
                      </>
                    )}
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <Button 
          onClick={submitProfile}
          disabled={submitting || !formData.medicalLicenseNumber || !formData.specialization || !formData.yearsOfExperience}
          className="w-full"
          size="lg"
        >
          {submitting ? 'Creating Profile...' : 'Submit Profile for Review'}
        </Button>
      </CardContent>
    </Card>
  );
}