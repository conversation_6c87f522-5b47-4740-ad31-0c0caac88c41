import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export const FixUserRole = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const fixUserRole = async () => {
    setLoading(true);
    
    try {
      console.log('Starting user role <NAME_EMAIL>');
      
      // First, get the user <NAME_EMAIL>
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', '<EMAIL>')
        .maybeSingle();

      console.log('Profile lookup result:', { profile, profileError });

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error finding user profile:', profileError);
        toast({
          title: "Error",
          description: "Database error while looking up user profile",
          variant: "destructive"
        });
        return;
      }

      if (!profile) {
        console.log('No profile found, will create one');
        // Get current user ID from auth
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError || !user) {
          toast({
            title: "Error",
            description: "Could not get current user information",
            variant: "destructive"
          });
          return;
        }

        // Create profile first
        const { data: newProfile, error: createProfileError } = await supabase
          .from('profiles')
          .insert({
            user_id: user.id,
            email: '<EMAIL>',
            full_name: 'Dr. Willy',
            role: 'doctor'
          })
          .select()
          .single();

        if (createProfileError) {
          console.error('Error creating profile:', createProfileError);
          toast({
            title: "Error",
            description: "Failed to create user profile",
            variant: "destructive"
          });
          return;
        }

        console.log('Created new profile:', newProfile);
        var userId = user.id;
      } else {
        var userId = profile.user_id;

      }

      // Update the role to 'doctor' if profile exists and isn't already doctor
      if (profile && profile.role !== 'doctor') {
        const { data: updatedProfile, error: updateError } = await supabase
          .from('profiles')
          .update({ role: 'doctor' })
          .eq('user_id', userId)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating profile role:', updateError);
          toast({
            title: "Error",
            description: "Failed to update user role",
            variant: "destructive"
          });
          return;
        }

        console.log('Updated profile:', updatedProfile);
      }

      // Check if user exists in doctors table, if not create entry
      const { data: doctorRecord, error: doctorCheckError } = await supabase
        .from('doctors')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      if (!doctorRecord) {
        // Doctor record doesn't exist, create one
        const { data: newDoctor, error: createDoctorError } = await supabase
          .from('doctors')
          .insert({
            user_id: userId,
            medical_license_number: 'TEMP-LICENSE-001',
            specialization: 'General Practice',
            years_of_experience: 5,
            clinic_address: 'To be updated',
            bio: 'Doctor profile to be completed',
            is_verified: true,
            is_available: true
          })
          .select()
          .single();

        if (createDoctorError) {
          console.error('Error creating doctor record:', createDoctorError);
          toast({
            title: "Warning",
            description: "Updated role but failed to create doctor record",
            variant: "destructive"
          });
        } else {
          console.log('Created doctor record:', newDoctor);
        }
      }

      toast({
        title: "Success",
        description: "User role updated to doctor successfully!",
      });

      // Refresh the page to reload user data
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Error", 
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Fix User Role</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          This <NAME_EMAIL> from 'customer' to 'doctor' role.
        </p>
        <Button 
          onClick={fixUserRole} 
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Updating...' : 'Update Role to Doctor'}
        </Button>
      </CardContent>
    </Card>
  );
};