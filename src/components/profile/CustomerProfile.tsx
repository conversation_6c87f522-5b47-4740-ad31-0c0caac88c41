import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ProfilePhotoUpload } from '@/components/profile/ProfilePhotoUpload'
import { 
  Edit3, 
  Phone,
  Mail,
  Calendar,
  FileText,
  Loader2,
  User
} from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface ProfileData {
  id: string
  user_id: string
  full_name?: string
  email: string
  phone?: string
  date_of_birth?: string
  address?: string
  national_id?: string
  created_at: string
  avatar_url?: string
  cover_url?: string
}

export default function CustomerProfile() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [profileData, setProfileData] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchProfile()
    }
  }, [user])

  const fetchProfile = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle()

      if (error) throw error

      if (data) {
        // Get profile photos
        const { data: avatarData } = supabase.storage
          .from('profile-photos')
          .getPublicUrl(`${user.id}/avatar.jpg`)
        
        const { data: coverData } = supabase.storage
          .from('profile-photos') 
          .getPublicUrl(`${user.id}/cover.jpg`)

        setProfileData({
          ...data,
          avatar_url: avatarData.publicUrl,
          cover_url: coverData.publicUrl
        })
      }
    } catch (error: any) {
      console.error('Error fetching profile:', error)
      toast({
        title: "Error loading profile",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePhotoUpdate = (url: string, type: 'avatar' | 'cover') => {
    if (profileData) {
      setProfileData({
        ...profileData,
        [type === 'avatar' ? 'avatar_url' : 'cover_url']: url
      })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (!profileData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Profile not found. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto max-w-4xl">
        {/* Cover Photo Section */}
        <div className="relative">
          <ProfilePhotoUpload
            userId={user?.id || ''}
            currentPhotoUrl={profileData.cover_url}
            onPhotoUpdate={(url) => handlePhotoUpdate(url, 'cover')}
            type="cover"
          />
          
          {/* Profile Header */}
          <div className="absolute -bottom-16 left-8 flex items-end gap-6">
            <ProfilePhotoUpload
              userId={user?.id || ''}
              currentPhotoUrl={profileData.avatar_url}
              onPhotoUpdate={(url) => handlePhotoUpdate(url, 'avatar')}
              size="lg"
              type="avatar"
            />
            
            <div className="pb-4">
              <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
                {profileData.full_name || 'Your Name'}
              </h1>
              <div className="flex items-center gap-2 mb-2">
                <User className="w-4 h-4 text-white" />
                <span className="text-white drop-shadow">Patient</span>
              </div>
            </div>
          </div>

          {/* Edit Button */}
          <div className="absolute top-4 right-4">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => navigate('/profile/edit')}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="pt-20 px-6 pb-6 space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2 space-y-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <p className="font-medium">Full Name</p>
                      <p className="text-muted-foreground">{profileData.full_name || 'Not provided'}</p>
                    </div>
                    
                    <div>
                      <p className="font-medium">Date of Birth</p>
                      <p className="text-muted-foreground">
                        {profileData.date_of_birth ? new Date(profileData.date_of_birth).toLocaleDateString() : 'Not provided'}
                      </p>
                    </div>
                    
                    <div>
                      <p className="font-medium">National ID</p>
                      <p className="text-muted-foreground">{profileData.national_id || 'Not provided'}</p>
                    </div>
                    
                    <div>
                      <p className="font-medium">Address</p>
                      <p className="text-muted-foreground">{profileData.address || 'Not provided'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{profileData.email}</span>
                  </div>
                  
                  {profileData.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{profileData.phone}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/consultation/doctors')}
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Find Doctors
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/apply')}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Apply for License
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/documents')}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    My Documents
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}