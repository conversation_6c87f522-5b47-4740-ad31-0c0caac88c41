import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Camera, Upload } from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface ProfilePhotoUploadProps {
  userId: string
  currentPhotoUrl?: string
  onPhotoUpdate?: (url: string) => void
  size?: 'sm' | 'md' | 'lg'
  type?: 'avatar' | 'cover'
}

export function ProfilePhotoUpload({ 
  userId, 
  currentPhotoUrl, 
  onPhotoUpdate, 
  size = 'md',
  type = 'avatar' 
}: ProfilePhotoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const { toast } = useToast()

  const sizeClasses = {
    sm: 'h-16 w-16',
    md: 'h-24 w-24', 
    lg: 'h-32 w-32'
  }

  const coverClasses = type === 'cover' ? 'w-full h-48 rounded-lg' : `${sizeClasses[size]} rounded-full`

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true)
      
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.')
      }

      const file = event.target.files[0]
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}/${type}.${fileExt}`

      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('profile-photos')
        .upload(fileName, file, { 
          upsert: true,
          cacheControl: '3600'
        })

      if (uploadError) {
        throw uploadError
      }

      // Get public URL
      const { data } = supabase.storage
        .from('profile-photos')
        .getPublicUrl(fileName)

      onPhotoUpdate?.(data.publicUrl)
      
      toast({
        title: "Photo uploaded successfully",
        description: `Your ${type} photo has been updated.`,
      })
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setUploading(false)
    }
  }

  if (type === 'cover') {
    return (
      <div className={`relative ${coverClasses} bg-muted group cursor-pointer overflow-hidden`}>
        {currentPhotoUrl && (
          <img 
            src={currentPhotoUrl} 
            alt="Cover photo" 
            className="w-full h-full object-cover"
          />
        )}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <label htmlFor={`cover-upload-${userId}`} className="cursor-pointer">
            <Button variant="secondary" size="sm" disabled={uploading}>
              <Camera className="w-4 h-4 mr-2" />
              {uploading ? 'Uploading...' : 'Change Cover'}
            </Button>
            <input
              id={`cover-upload-${userId}`}
              type="file"
              accept="image/*"
              className="sr-only"
              onChange={handleFileUpload}
              disabled={uploading}
            />
          </label>
        </div>
      </div>
    )
  }

  return (
    <div className="relative group">
      <Avatar className={coverClasses}>
        <AvatarImage src={currentPhotoUrl} />
        <AvatarFallback className="text-lg font-semibold bg-primary text-primary-foreground">
          {userId.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-full flex items-center justify-center">
        <label htmlFor={`avatar-upload-${userId}`} className="cursor-pointer">
          <Button variant="secondary" size="sm" disabled={uploading}>
            <Upload className="w-4 h-4" />
          </Button>
          <input
            id={`avatar-upload-${userId}`}
            type="file"
            accept="image/*"
            className="sr-only"
            onChange={handleFileUpload}
            disabled={uploading}
          />
        </label>
      </div>
    </div>
  )
}