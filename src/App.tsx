import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import { Layout } from "@/components/layout/Layout";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Process from "./pages/Process";
import FindDoctors from "./pages/FindDoctors";
import SignIn from "./pages/auth/SignIn";
import SignUp from "./pages/auth/SignUp";
import DoctorSignUp from "./pages/auth/DoctorSignUp";
import ForgotPassword from "./pages/auth/ForgotPassword";
import ResetPassword from "./pages/auth/ResetPassword";
import DoctorReview from "./pages/admin/DoctorReview";
import InterviewScheduling from "./pages/admin/InterviewScheduling";
import AdminPortal from "./pages/admin/AdminPortal";
import DoctorManagement from "./pages/admin/DoctorManagement";
import CustomerManagement from "./pages/admin/CustomerManagement";
import DoctorOnboarding from "./pages/doctor/DoctorOnboarding";
import DoctorConsultations from "./pages/doctor/DoctorConsultations";
import DoctorDashboard from "./pages/doctor/DoctorDashboard";
import DoctorPortal from "./pages/doctor/DoctorPortal";
import DoctorSelection from "./pages/consultation/DoctorSelection";
import LicenseApplication from "./pages/LicenseApplication";
import Profile from "./pages/Profile";
import ProfileEdit from "./pages/ProfileEdit";
import Documents from "./pages/Documents";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Layout>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/about" element={<About />} />
              <Route path="/process" element={<Process />} />
              <Route path="/doctors" element={<FindDoctors />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/auth/signin" element={<SignIn />} />
              <Route path="/auth/signup" element={<SignUp />} />
              <Route path="/auth/doctor-signup" element={<DoctorSignUp />} />
              <Route path="/auth/forgot-password" element={<ForgotPassword />} />
              <Route path="/auth/reset-password" element={<ResetPassword />} />
              <Route path="/admin" element={<AdminPortal />} />
              <Route path="/admin/doctor-review" element={<DoctorReview />} />
              <Route path="/admin/interview-scheduling" element={<InterviewScheduling />} />
              <Route path="/admin/doctors" element={<DoctorManagement />} />
              <Route path="/admin/customers" element={<CustomerManagement />} />
              <Route path="/doctor" element={<DoctorPortal />} />
              <Route path="/doctor/dashboard" element={<DoctorDashboard />} />
              <Route path="/doctor/onboarding" element={<DoctorOnboarding />} />
              <Route path="/doctor/consultations" element={<DoctorConsultations />} />
              <Route path="/consultation/doctors" element={<DoctorSelection />} />
              <Route path="/apply" element={<LicenseApplication />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/profile/edit" element={<ProfileEdit />} />
              <Route path="/documents" element={<Documents />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Layout>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
