// Shared types for database entities

export interface DoctorBase {
  id: string;
  user_id: string;
  bio?: string;
  specialization: string;
  years_of_experience: number;
  consultation_fee?: number;
  calendly_link?: string;
  clinic_address?: string;
  status: string;
  is_verified: boolean;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  interview_scheduled_at?: string;
  medical_license_number: string;
}

export interface DoctorWithProfile extends DoctorB<PERSON> {
  profiles?: {
    full_name: string;
    email: string;
    phone?: string;
  };
}

export interface ProfileBase {
  id: string;
  user_id: string;
  full_name?: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  address?: string;
  national_id?: string;
  role: 'customer' | 'doctor' | 'admin';
  created_at: string;
  updated_at: string;
}

// For backward compatibility with existing admin interfaces
export interface DoctorLegacy {
  id: string;
  user_id: string;
  profile_id?: string;
  medical_license_document_url?: string;
  national_id_document_url?: string;
  approved_at?: string;
  bio?: string;
  specialization: string;
  years_of_experience: number;
  consultation_fee?: number;
  calendly_link?: string;
  clinic_address?: string;
  status: string;
  is_verified: boolean;
  is_available: boolean;
  is_active: boolean; // Map from is_available
  created_at: string;
  updated_at: string;
  interview_scheduled_at?: string;
  medical_license_number: string;
  profiles?: {
    full_name: string;
    email: string;
    phone?: string;
  };
}