import { supabase } from "@/integrations/supabase/client";
import { User } from "@supabase/supabase-js";

export type UserRole = 'customer' | 'doctor' | 'admin';

export interface AuthUser extends User {
  role?: UserRole;
  profile?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
    role: UserRole;
  };
}

export const signUp = async (email: string, password: string, userData: {
  full_name: string;
  phone?: string;
  role?: UserRole;
}) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/signin`,
      data: {
        full_name: userData.full_name,
        phone: userData.phone,
        role: userData.role || 'customer'
      }
    }
  });

  if (error) throw error;

  // Create profile record
  if (data.user) {
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: data.user.id,
        email: email,
        full_name: userData.full_name,
        phone: userData.phone,
        role: userData.role || 'customer'
      });

    if (profileError) {
      console.error('Profile creation error:', profileError);
    }
  }

  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async (): Promise<AuthUser | null> => {
  console.log('getCurrentUser: Starting...');
  
  try {
    console.log('getCurrentUser: Using session-based approach with timeout...');
    
    // Add timeout to session call as well
    const sessionPromise = supabase.auth.getSession();
    const sessionTimeoutPromise = new Promise((_, reject) => 
      setTimeout(() => {
        console.log('getCurrentUser: Session call timed out after 2 seconds');
        reject(new Error('Session timeout'));
      }, 2000)
    );
    
    const sessionResult = await Promise.race([sessionPromise, sessionTimeoutPromise]);
    const { data: { session }, error: sessionError } = sessionResult as any;
    
    console.log('getCurrentUser: Session fetched', { session: !!session, sessionError });
    
    if (sessionError) {
      console.error('getCurrentUser: Session error:', sessionError);
      return null;
    }
    
    if (!session?.user) {
      console.log('getCurrentUser: No session or user found');
      return null;
    }
    
    const user = session.user;
    console.log('getCurrentUser: Got user from session', { userId: user.id });

    console.log('getCurrentUser: Fetching profile for user:', user.id);
    
    // Fetch user profile with timeout
    const profilePromise = supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();
    
    const profileTimeoutPromise = new Promise((_, reject) => 
      setTimeout(() => {
        console.log('getCurrentUser: Profile fetch timed out after 2 seconds');
        reject(new Error('Profile timeout'));
      }, 2000)
    );

    const { data: profile, error: profileError } = await Promise.race([
      profilePromise, 
      profileTimeoutPromise
    ]) as any;

    console.log('getCurrentUser: Profile fetch result', { profile, profileError });

    if (profileError) {
      console.error('getCurrentUser: Profile error:', profileError);
      // Return user without profile if profile fetch fails
      return {
        ...user,
        profile: null
      } as AuthUser;
    }

    const authUser = {
      ...user,
      profile
    } as AuthUser;

    console.log('getCurrentUser: Returning auth user with role:', authUser.profile?.role);
    return authUser;
  } catch (err) {
    console.error('getCurrentUser: Caught error:', err);
    
    // Fallback: try to get user from session storage or alternative method
    console.log('getCurrentUser: Attempting fallback method...');
    try {
      const session = await supabase.auth.getSession();
      console.log('getCurrentUser: Fallback session:', !!session.data.session);
      
      if (session.data.session?.user) {
        console.log('getCurrentUser: Using session user as fallback');
        return {
          ...session.data.session.user,
          profile: null
        } as AuthUser;
      }
    } catch (fallbackError) {
      console.error('getCurrentUser: Fallback also failed:', fallbackError);
    }
    
    return null;
  }
};

export const updateProfile = async (userId: string, updates: {
  full_name?: string;
  phone?: string;
  date_of_birth?: string;
  address?: string;
  national_id?: string;
}) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('user_id', userId)
    .select()
    .single();

  return { data, error };
};