import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { AuthUser, getCurrentUser } from '@/lib/auth';

interface AuthContextType {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const isRefreshingRef = useRef(false);

  const refreshUser = async () => {
    if (isRefreshingRef.current) {
      console.log('refreshUser: Already refreshing, skipping...');
      return;
    }
    
    console.log('refreshUser: Starting...');
    isRefreshingRef.current = true;
    
    try {
      const currentUser = await getCurrentUser();
      console.log('refreshUser: Got user', { 
        user: !!currentUser, 
        role: currentUser?.profile?.role,
        profile: currentUser?.profile 
      });
      setUser(currentUser);
    } catch (error) {
      console.error('refreshUser: Error refreshing user:', error);
      setUser(null);
    } finally {
      console.log('refreshUser: Completed');
      isRefreshingRef.current = false;
    }
  };

  useEffect(() => {
    // Get initial session
    const initializeAuth = async () => {
      try {
        console.log('useAuth: Getting initial session...');
        
        // Add timeout to initial session call too
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => {
            console.log('useAuth: Initial session call timed out');
            reject(new Error('Initial session timeout'));
          }, 2000)
        );
        
        const result = await Promise.race([sessionPromise, timeoutPromise]);
        const { data: { session }, error } = result as any;
        
        if (error) {
          console.error('useAuth: Session error:', error);
        }
        
        console.log('useAuth: Initial session', { session: !!session });
        setSession(session);
        
        if (session?.user) {
          console.log('useAuth: Initial session has user, refreshing...');
          await refreshUser();
        }
      } catch (error) {
        console.error('useAuth: Error getting initial session:', error);
        // Set a minimal user object if we're in a timeout situation
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, !!session);
        setSession(session);
        
        if (event === 'SIGNED_IN' && session?.user) {
          console.log('User signed in, fetching profile for:', session.user.id);
          
          try {
            // Add timeout to profile fetch
            const profilePromise = supabase
              .from('profiles')
              .select('*')
              .eq('user_id', session.user.id)
              .single();
            
            const timeoutPromise = new Promise((_, reject) => 
              setTimeout(() => {
                console.log('Profile fetch timed out');
                reject(new Error('Profile fetch timeout'));
              }, 3000)
            );
            
            console.log('Starting profile fetch...');
            const result = await Promise.race([profilePromise, timeoutPromise]);
            const { data: profile, error: profileError } = result as any;
            
            console.log('Profile fetch result:', { profile, profileError });
            
            if (profileError) {
              console.error('Profile fetch error:', profileError);
              // Set user without profile if profile fetch fails
              setUser({
                ...session.user,
                profile: null
              } as AuthUser);
            } else {
              console.log('Profile fetched successfully:', profile);
              setUser({
                ...session.user,
                profile
              } as AuthUser);
            }
          } catch (error) {
            console.error('Error fetching profile:', error);
            // Set user with minimal data to prevent hanging
            setUser({
              ...session.user,
              profile: null
            } as AuthUser);
          }
        } else if (event === 'SIGNED_OUT' || !session) {
          console.log('Clearing user due to SIGNED_OUT or no session');
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    setSession(null);
  };

  return (
    <AuthContext.Provider value={{ user, session, loading, signOut, refreshUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  console.log('useAuth called - context exists:', !!context);
  if (context === undefined) {
    console.error('useAuth called outside AuthProvider! Call stack:', new Error().stack);
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};