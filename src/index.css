@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Medical Cannabis Theme - Professional greens with gold accents */
    --background: 0 0% 100%;
    --foreground: 160 8% 15%;

    --card: 0 0% 100%;
    --card-foreground: 160 8% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 160 8% 15%;

    /* Deep forest green with gold highlights */
    --primary: 160 60% 25%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 160 40% 35%;
    --primary-dark: 160 80% 15%;

    /* Soft sage green */
    --secondary: 160 15% 85%;
    --secondary-foreground: 160 8% 25%;

    --muted: 160 10% 92%;
    --muted-foreground: 160 5% 45%;

    /* Gold accent for premium feel */
    --accent: 45 90% 60%;
    --accent-foreground: 160 8% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 160 15% 88%;
    --input: 160 15% 92%;
    --ring: 160 60% 25%;

    /* Medical grade status colors */
    --success: 120 60% 45%;
    --success-foreground: 0 0% 98%;
    --warning: 45 100% 50%;
    --warning-foreground: 45 10% 15%;
    --info: 200 100% 45%;
    --info-foreground: 0 0% 98%;

    --radius: 0.75rem;

    /* Gradients for premium feel */
    --gradient-primary: linear-gradient(135deg, hsl(160 60% 25%), hsl(160 40% 35%));
    --gradient-secondary: linear-gradient(135deg, hsl(160 15% 85%), hsl(160 10% 92%));
    --gradient-accent: linear-gradient(135deg, hsl(45 90% 60%), hsl(45 80% 70%));

    --sidebar-background: 160 20% 96%;
    --sidebar-foreground: 160 8% 25%;
    --sidebar-primary: 160 60% 25%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 160 15% 90%;
    --sidebar-accent-foreground: 160 8% 25%;
    --sidebar-border: 160 15% 88%;
    --sidebar-ring: 160 60% 25%;
  }

  .dark {
    /* Dark mode with sophisticated medical cannabis theme */
    --background: 160 15% 8%;
    --foreground: 160 5% 90%;

    --card: 160 15% 10%;
    --card-foreground: 160 5% 90%;

    --popover: 160 15% 10%;
    --popover-foreground: 160 5% 90%;

    --primary: 160 50% 45%;
    --primary-foreground: 160 15% 8%;
    --primary-light: 160 40% 55%;
    --primary-dark: 160 60% 35%;

    --secondary: 160 10% 15%;
    --secondary-foreground: 160 5% 85%;

    --muted: 160 8% 12%;
    --muted-foreground: 160 5% 60%;

    --accent: 45 80% 55%;
    --accent-foreground: 160 15% 8%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 98%;

    --success: 120 50% 50%;
    --success-foreground: 0 0% 98%;
    --warning: 45 90% 60%;
    --warning-foreground: 45 15% 10%;
    --info: 200 80% 55%;
    --info-foreground: 0 0% 98%;

    --border: 160 10% 20%;
    --input: 160 10% 15%;
    --ring: 160 50% 45%;

    --gradient-primary: linear-gradient(135deg, hsl(160 50% 45%), hsl(160 40% 55%));
    --gradient-secondary: linear-gradient(135deg, hsl(160 10% 15%), hsl(160 8% 12%));
    --gradient-accent: linear-gradient(135deg, hsl(45 80% 55%), hsl(45 70% 65%));

    --sidebar-background: 160 15% 6%;
    --sidebar-foreground: 160 5% 85%;
    --sidebar-primary: 160 50% 45%;
    --sidebar-primary-foreground: 160 15% 8%;
    --sidebar-accent: 160 10% 12%;
    --sidebar-accent-foreground: 160 5% 85%;
    --sidebar-border: 160 10% 20%;
    --sidebar-ring: 160 50% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}