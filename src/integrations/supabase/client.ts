// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://cvbxnvmnzhxibmwppcrk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN2Ynhudm1uemh4aWJtd3BwY3JrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQxMjY5NTEsImV4cCI6MjA2OTcwMjk1MX0.TSeg6DdqFA22TR0EpsIBsz7iHPdlSKsE_KJKUn14lsE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});