import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  UserCheck, 
  Calendar, 
  Shield, 
  CheckCircle, 
  Clock, 
  ArrowRight,
  Leaf,
  Stethoscope,
  CreditCard
} from 'lucide-react';

const processSteps = [
  {
    id: 1,
    title: "Application Submission",
    description: "Complete your medical cannabis license application with required documentation",
    icon: FileText,
    duration: "15-30 minutes",
    status: "start",
    details: [
      "Fill out personal information",
      "Provide medical condition details",
      "Upload required documents",
      "Submit application fee"
    ]
  },
  {
    id: 2,
    title: "Medical Review",
    description: "Our certified doctors review your application and medical history",
    icon: Stethoscope,
    duration: "2-3 business days",
    status: "review",
    details: [
      "Medical history evaluation",
      "Condition verification",
      "Treatment assessment",
      "Eligibility determination"
    ]
  },
  {
    id: 3,
    title: "Doctor Consultation",
    description: "Schedule and attend a consultation with an approved cannabis doctor",
    icon: Calendar,
    duration: "30-45 minutes",
    status: "consultation",
    details: [
      "Choose available doctor",
      "Book consultation slot",
      "Attend video/in-person meeting",
      "Discuss treatment options"
    ]
  },
  {
    id: 4,
    title: "Final Approval",
    description: "Receive your official medical cannabis license and recommendations",
    icon: Shield,
    duration: "1-2 business days",
    status: "approval",
    details: [
      "Application approval notification",
      "Digital license issuance",
      "Treatment recommendations",
      "Dispensary access activation"
    ]
  }
];

const faqs = [
  {
    question: "How long does the entire process take?",
    answer: "The complete process typically takes 5-7 business days from application submission to license approval."
  },
  {
    question: "What medical conditions qualify?",
    answer: "We support a wide range of conditions including chronic pain, epilepsy, cancer, anxiety, PTSD, and arthritis."
  },
  {
    question: "Is the consultation covered by insurance?",
    answer: "While consultations may not be covered by traditional insurance, we offer affordable pricing and payment plans."
  },
  {
    question: "Can I use my license immediately?",
    answer: "Yes, once approved, your digital license is immediately valid at participating dispensaries."
  }
];

export default function Process() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container mx-auto px-6 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-gradient-primary rounded-2xl">
                <Leaf className="w-12 h-12 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Simple & Secure Process
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Get your medical cannabis license in 4 easy steps. Our streamlined process 
              ensures you receive proper care and legal access to medical cannabis.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={() => navigate('/auth/signup')}
                className="hover-scale"
              >
                Start Application
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => navigate('/consultation/doctors')}
                className="hover-scale"
              >
                Find Doctors
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              How It Works
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our carefully designed process ensures you get the medical cannabis license 
              you need while maintaining the highest standards of care.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {processSteps.map((step, index) => (
              <Card 
                key={step.id} 
                className="relative overflow-hidden hover-scale transition-all duration-300 hover:shadow-elegant animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader className="text-center pb-4">
                  <div className="relative mx-auto w-16 h-16 mb-4">
                    <div className="absolute inset-0 bg-gradient-primary rounded-2xl opacity-10" />
                    <div className="relative w-full h-full bg-primary/10 rounded-2xl flex items-center justify-center">
                      <step.icon className="w-8 h-8 text-primary" />
                    </div>
                    <Badge 
                      variant="secondary" 
                      className="absolute -top-2 -right-2 w-8 h-8 rounded-full p-0 flex items-center justify-center"
                    >
                      {step.id}
                    </Badge>
                  </div>
                  
                  <CardTitle className="text-lg mb-2">{step.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {step.description}
                  </CardDescription>
                  
                  <div className="flex items-center justify-center gap-2 mt-3">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground font-medium">
                      {step.duration}
                    </span>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <ul className="space-y-2">
                    {step.details.map((detail, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-muted-foreground">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Requirements Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              What You'll Need
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Prepare these items before starting your application to ensure a smooth process.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto">
            {[
              {
                title: "Valid ID",
                description: "Government-issued photo identification",
                icon: UserCheck
              },
              {
                title: "Medical Records",
                description: "Documentation of qualifying medical condition",
                icon: FileText
              },
              {
                title: "Payment Method",
                description: "Credit card or bank account for fees",
                icon: CreditCard
              }
            ].map((requirement, index) => (
              <Card 
                key={requirement.title}
                className="text-center hover-scale animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                    <requirement.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{requirement.title}</CardTitle>
                  <CardDescription>{requirement.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Get answers to common questions about our medical cannabis license process.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 max-w-4xl mx-auto">
            {faqs.map((faq, index) => (
              <Card 
                key={index}
                className="hover-scale animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader>
                  <CardTitle className="text-lg text-left">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10">
        <div className="container mx-auto px-6 text-center">
          <div className="animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of patients who have successfully obtained their medical cannabis 
              license through our trusted platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={() => navigate('/auth/signup')}
                className="hover-scale"
              >
                Begin Application
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => navigate('/about')}
                className="hover-scale"
              >
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}