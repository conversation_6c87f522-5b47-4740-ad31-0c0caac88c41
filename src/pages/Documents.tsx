import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, FileText, ArrowLeft, Download, Upload } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface Document {
  id: string;
  name: string;
  type: string;
  upload_date: string;
  status: 'pending' | 'approved' | 'rejected';
  file_url?: string;
}

interface Application {
  id: string;
  status?: string;
  medical_condition?: string;
  symptoms?: string[];
  previous_treatments?: string[];
  doctor_referral?: string;
  created_at: string;
  approved_at?: string;
  rejected_at?: string;
}

export default function Documents() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [applications, setApplications] = useState<Application[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  const fetchData = async () => {
    if (!user) return;

    try {
      // Fetch doctor applications (using doctors table as placeholder)
      const { data: applicationsData, error: applicationsError } = await supabase
        .from('doctors')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (applicationsError) throw applicationsError;
      setApplications(applicationsData || []);

      // Note: Documents table would need to be created in the database
      // For now, we'll show a placeholder
      setDocuments([]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string, approvedAt?: string, rejectedAt?: string) => {
    if (rejectedAt) {
      return <Badge variant="destructive">Rejected</Badge>;
    }
    if (approvedAt) {
      return <Badge variant="default">Approved</Badge>;
    }
    return <Badge variant="secondary">Pending Review</Badge>;
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Please sign in to view your documents.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate('/dashboard')} className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <FileText className="w-8 h-8 text-primary" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-foreground">My Documents</h1>
            <p className="text-muted-foreground mt-2">
              View your applications, licenses, and uploaded documents
            </p>
          </div>
        </div>

        <div className="space-y-6">
          {/* License Applications */}
          <Card>
            <CardHeader>
              <CardTitle>License Applications</CardTitle>
              <CardDescription>
                Track the status of your medical cannabis license applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {applications.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">No applications found</p>
                  <Button onClick={() => navigate('/apply')}>
                    Apply for License
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {applications.map((application) => (
                    <div key={application.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">License Application</h3>
                          <p className="text-sm text-muted-foreground">
                            Condition: {application.medical_condition || 'Not specified'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Applied: {new Date(application.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        {getStatusBadge(application.status, application.approved_at, application.rejected_at)}
                      </div>
                      
                      {application.approved_at && (
                        <Alert className="mt-3">
                          <AlertDescription>
                            Your application has been approved! You can now book consultations with approved doctors.
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      {application.rejected_at && (
                        <Alert variant="destructive" className="mt-3">
                          <AlertDescription>
                            Your application was not approved. Please contact support for more information.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Uploaded Documents */}
          <Card>
            <CardHeader>
              <CardTitle>Uploaded Documents</CardTitle>
              <CardDescription>
                Medical records, prescriptions, and other supporting documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              {documents.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">No documents uploaded yet</p>
                  <Button variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {documents.map((document) => (
                    <div key={document.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">{document.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Uploaded: {new Date(document.upload_date).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            document.status === 'approved' ? 'default' :
                            document.status === 'rejected' ? 'destructive' : 'secondary'
                          }>
                            {document.status}
                          </Badge>
                          {document.file_url && (
                            <Button variant="outline" size="sm">
                              <Download className="w-4 h-4 mr-2" />
                              Download
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-3">
                <Button variant="outline" onClick={() => navigate('/apply')}>
                  <FileText className="w-4 h-4 mr-2" />
                  New Application
                </Button>
                <Button variant="outline">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Document
                </Button>
                <Button variant="outline" onClick={() => navigate('/consultation/doctors')}>
                  <FileText className="w-4 h-4 mr-2" />
                  Book Consultation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}