import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Leaf, Calendar, FileText, Settings, LogOut } from 'lucide-react';
import { FixUserRole } from '@/components/admin/FixUserRole';

export default function Dashboard() {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();

  // Redirect doctors to their specific dashboard
  React.useEffect(() => {
    console.log('Dashboard: User data changed', { 
      user: !!user, 
      role: user?.profile?.role, 
      loading,
      userProfile: user?.profile,
      userEmail: user?.email,
      userId: user?.id
    });
    
    // Debug the specific role check
    if (user?.profile) {
      console.log('Dashboard: Profile exists, role is:', user.profile.role);
      console.log('Dashboard: Is doctor?', user.profile.role === 'doctor');
    } else {
      console.log('Dashboard: No profile found for user');
    }
    
    if (!loading && user?.profile?.role === 'doctor') {
      console.log('Dashboard: Redirecting doctor to doctor dashboard');
      navigate('/doctor/dashboard');
    }
  }, [user, loading, navigate]);

  const handleSignOut = async () => {
    await signOut();
    window.location.href = '/';
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Please sign in to access your dashboard.</p>
        </div>
      </div>
    );
  }

  const isDoctor = user.profile?.role === 'doctor';
  const isAdmin = user.profile?.role === 'admin';

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Welcome back, {user.profile?.full_name || user.email}
            </h1>
            <p className="text-muted-foreground">
              {isDoctor ? 'Doctor Dashboard' : isAdmin ? 'Admin Dashboard' : 'Customer Dashboard'}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              {user.profile?.role || 'customer'}
            </Badge>
            <Button variant="outline" onClick={handleSignOut}>
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>

        {/* Show role fix <NAME_EMAIL> */}
        {user.email === '<EMAIL>' && (!user.profile || user.profile?.role === 'customer') && (
          <div className="mb-6">
            <FixUserRole />
          </div>
        )}

        {/* Dashboard Content */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Profile Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Manage your account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm font-medium">Email</p>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Role</p>
                <p className="text-sm text-muted-foreground capitalize">{user.profile?.role || 'customer'}</p>
              </div>
              {user.profile?.phone && (
                <div>
                  <p className="text-sm font-medium">Phone</p>
                  <p className="text-sm text-muted-foreground">{user.profile.phone}</p>
                </div>
              )}
              <Button variant="outline" size="sm" className="w-full mt-4" onClick={() => navigate('/profile/edit')}>
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          {/* License Status (Customer) */}
          {!isDoctor && !isAdmin && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Leaf className="w-5 h-5" />
                  License Status
                </CardTitle>
                <CardDescription>
                  Your medical cannabis license information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Badge variant="outline" className="w-full justify-center py-2">
                    No Active License
                  </Badge>
                  <p className="text-sm text-muted-foreground text-center">
                    Apply for your medical cannabis license to get started
                  </p>
                  <Button className="w-full" onClick={() => navigate('/apply')}>
                    Apply for License
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Doctor Stats */}
          {isDoctor && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Patient Statistics
                </CardTitle>
                <CardDescription>
                  Your consultation metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center">
                    <p className="text-2xl font-bold">0</p>
                    <p className="text-sm text-muted-foreground">Consultations</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">0</p>
                    <p className="text-sm text-muted-foreground">Active Patients</p>
                  </div>
                  <Button variant="outline" className="w-full" onClick={() => navigate('/doctor/consultations')}>
                    View All Patients
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Appointments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                {isDoctor ? 'Schedule' : 'Appointments'}
              </CardTitle>
              <CardDescription>
                {isDoctor ? 'Manage your consultation schedule' : 'Your upcoming appointments'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground text-center">
                  No upcoming appointments
                </p>
                <Button variant="outline" className="w-full" onClick={() => navigate(isDoctor ? '/doctor/onboarding' : '/consultation/doctors')}>
                  {isDoctor ? 'Manage Schedule' : 'Book Appointment'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-3">
              {!isDoctor && !isAdmin && (
                <>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/apply')}>
                    <Leaf className="w-4 h-4 mr-2" />
                    Apply for License
                  </Button>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/consultation/doctors')}>
                    <Calendar className="w-4 h-4 mr-2" />
                    Book Consultation
                  </Button>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/documents')}>
                    <FileText className="w-4 h-4 mr-2" />
                    View Documents
                  </Button>
                </>
              )}
              {isDoctor && (
                <>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/doctor/onboarding')}>
                    <Calendar className="w-4 h-4 mr-2" />
                    Set Availability
                  </Button>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/doctor/consultations')}>
                    <FileText className="w-4 h-4 mr-2" />
                    Patient Records
                  </Button>
                  <Button variant="outline" className="justify-start" onClick={() => navigate('/profile/edit')}>
                    <Settings className="w-4 h-4 mr-2" />
                    Update Profile
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}