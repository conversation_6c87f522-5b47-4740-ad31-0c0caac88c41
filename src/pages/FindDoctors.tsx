import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Stethoscope, 
  MapPin, 
  Clock, 
  Star, 
  Calendar, 
  Search,
  Filter,
  Award,
  Users,
  Leaf,
  CheckCircle,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { DoctorWithProfile } from '@/types/database';

type Doctor = DoctorWithProfile;

const specializations = [
  "Pain Management", 
  "Oncology", 
  "Neurology", 
  "Psychiatry", 
  "Internal Medicine", 
  "Family Medicine"
];

const locations = [
  "New York", 
  "Los Angeles", 
  "Chicago", 
  "Houston", 
  "Phoenix", 
  "Philadelphia"
];

export default function FindDoctors() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctors = async () => {
    try {
      const { data, error } = await supabase
        .from('doctors')
        .select(`
          *,
          profiles!user_id(full_name, email)
        `)
        .eq('is_verified', true)
        .eq('is_available', true)
        .not('calendly_link', 'is', null);

      if (error) throw error;
      
      const doctorsWithProfile = data?.map(doctor => ({
        ...doctor,
        profiles: Array.isArray(doctor.profiles) ? doctor.profiles[0] : doctor.profiles
      })) || []
      
      setDoctors(doctorsWithProfile);
    } catch (error) {
      console.error('Error fetching doctors:', error);
      toast({
        title: "Error",
        description: "Failed to load doctors. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch = doctor.profiles?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doctor.bio?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    
    const matchesSpecialization = selectedSpecialization === 'all-specializations' || !selectedSpecialization || 
      doctor.specialization.toLowerCase().includes(selectedSpecialization.toLowerCase());
    
    return matchesSearch && matchesSpecialization;
  });

  const handleBookConsultation = async (doctor: Doctor) => {
    if (!user) {
      toast({
        title: "Sign In Required",
        description: "Please sign in to book a consultation.",
        variant: "destructive",
      });
      navigate('/auth/signin');
      return;
    }

    try {
      // Create a consultation request (using profiles table for now)
      const { error } = await supabase
        .from('profiles')
        .update({
          // Store consultation request in profile notes for now
          address: `Consultation requested with doctor: ${doctor.profiles?.full_name}`
        })
        .eq('user_id', user.id);

      if (error) throw error;

      // Open Calendly link
      if (doctor.calendly_link) {
        window.open(doctor.calendly_link, '_blank');
        toast({
          title: "Booking Opened",
          description: `Opening booking page for Dr. ${doctor.profiles?.full_name}`,
        });
      }
    } catch (error) {
      console.error('Error booking consultation:', error);
      toast({
        title: "Error",
        description: "Failed to initiate booking. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container mx-auto px-6 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-gradient-primary rounded-2xl">
                <Stethoscope className="w-12 h-12 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Find Your Doctor
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Connect with certified medical cannabis doctors who understand your needs 
              and can provide expert guidance for your treatment journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={() => navigate('/auth/signup')}
                className="hover-scale"
              >
                Get Started
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => navigate('/process')}
                className="hover-scale"
              >
                Learn Process
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3 max-w-4xl mx-auto">
            {[
              { icon: Users, number: "500+", label: "Certified Doctors" },
              { icon: Award, number: "98%", label: "Success Rate" },
              { icon: CheckCircle, number: "10K+", label: "Patients Helped" }
            ].map((stat, index) => (
              <Card 
                key={stat.label}
                className="text-center hover-scale animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardContent className="pt-6">
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                    <stat.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-foreground mb-2">{stat.number}</div>
                  <div className="text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-12 bg-background">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8 animate-fade-in">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                Find the Right Doctor for You
              </h2>
              <p className="text-lg text-muted-foreground">
                Search by name, specialization, or location to find your perfect match.
              </p>
            </div>

            <Card className="p-6 animate-fade-in">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="relative">
                  <Search className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search doctors..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedSpecialization} onValueChange={setSelectedSpecialization}>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Filter className="w-4 h-4" />
                      <SelectValue placeholder="Specialization" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-specializations">All Specializations</SelectItem>
                    {specializations.map(spec => (
                      <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      <SelectValue placeholder="Location" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-locations">All Locations</SelectItem>
                    {locations.map(location => (
                      <SelectItem key={location} value={location}>{location}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Doctors Grid */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : filteredDoctors.length === 0 ? (
            <div className="text-center py-20">
              <Stethoscope className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">No Doctors Found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or check back later.
              </p>
              <Button onClick={() => {
                setSearchTerm('');
                setSelectedSpecialization('all-specializations');
                setSelectedLocation('all-locations');
              }}>
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
              {filteredDoctors.map((doctor, index) => (
                <Card 
                  key={doctor.id}
                  className="overflow-hidden hover-scale transition-all duration-300 hover:shadow-elegant animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardHeader className="text-center pb-4">
                    <div className="mx-auto w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mb-4">
                      <Stethoscope className="w-10 h-10 text-primary-foreground" />
                    </div>
                    
                    <CardTitle className="text-xl mb-2">
                      Dr. {doctor.profiles?.full_name}
                    </CardTitle>
                    
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">(4.9)</span>
                    </div>

                    <div className="flex flex-wrap gap-1 justify-center mb-3">
                      <Badge variant="secondary" className="text-xs">
                        {doctor.specialization}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {doctor.bio && (
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {doctor.bio}
                      </p>
                    )}
                    
                    <div className="space-y-2">
                      {doctor.years_of_experience && (
                        <div className="flex items-center gap-2 text-sm">
                          <Award className="w-4 h-4 text-primary" />
                          <span>{doctor.years_of_experience} years experience</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="w-4 h-4 text-primary" />
                        <span>Available for consultations</span>
                      </div>
                      
                      {doctor.consultation_fee && (
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="w-4 h-4 text-primary" />
                          <span>From ${doctor.consultation_fee}</span>
                        </div>
                      )}
                    </div>

                    <div className="pt-4 space-y-2">
                      <Button 
                        className="w-full" 
                        onClick={() => handleBookConsultation(doctor)}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Book Consultation
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => navigate(`/doctors/${doctor.id}`)}
                      >
                        View Profile
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10">
        <div className="container mx-auto px-6 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <div className="p-3 bg-primary/10 rounded-full">
                <Leaf className="w-8 h-8 text-primary" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Ready to Start Your Journey?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Take the first step towards better health with personalized medical cannabis treatment.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={() => navigate('/apply')}
                className="hover-scale"
              >
                Apply for License
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => navigate('/contact')}
                className="hover-scale"
              >
                Have Questions?
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}