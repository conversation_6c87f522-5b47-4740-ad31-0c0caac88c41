import { useAuth } from '@/hooks/useAuth'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'
import Doctor<PERSON><PERSON><PERSON><PERSON> from '@/pages/doctor/DoctorProfile'
import CustomerProfile from '@/components/profile/CustomerProfile'

export default function Profile() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Please sign in to view your profile.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Route to appropriate profile based on user role
  if (user.role === 'doctor') {
    return <DoctorProfile />
  }

  return <CustomerProfile />
}