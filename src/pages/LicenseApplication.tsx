import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Leaf, ArrowLeft } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export default function LicenseApplication() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    medical_condition: '',
    symptoms: '',
    previous_treatments: '',
    doctor_referral: '',
    national_id: '',
    date_of_birth: '',
    address: '',
    phone: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);
    try {
      // Insert application data into profiles (as placeholder)
      const { error } = await supabase
        .from('profiles')
        .update({
          // Store application info in address field for now
          address: `License Application: ${formData.medical_condition}`
        })
        .eq('user_id', user.id);

      if (error) throw error;

      // Update profile with additional information
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          national_id: formData.national_id,
          date_of_birth: formData.date_of_birth,
          address: formData.address,
          phone: formData.phone
        })
        .eq('user_id', user.id);

      if (profileError) throw profileError;

      toast({
        title: "Application Submitted",
        description: "Your license application has been submitted successfully. You will be contacted for next steps.",
      });

      navigate('/dashboard');
    } catch (error) {
      console.error('Error submitting application:', error);
      toast({
        title: "Error",
        description: "Failed to submit application. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Please sign in to access the license application.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 max-w-2xl">
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate('/dashboard')} className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <Leaf className="w-8 h-8 text-primary" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-foreground">Medical Cannabis License Application</h1>
            <p className="text-muted-foreground mt-2">
              Please provide the required information to begin your application process
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Application Form</CardTitle>
            <CardDescription>
              All fields are required. Your information will be reviewed by our medical team.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Information</h3>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="national_id">National ID</Label>
                    <Input
                      id="national_id"
                      value={formData.national_id}
                      onChange={(e) => handleInputChange('national_id', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Medical Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Medical Information</h3>
                
                <div>
                  <Label htmlFor="medical_condition">Medical Condition</Label>
                  <Select onValueChange={(value) => handleInputChange('medical_condition', value)} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your medical condition" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chronic_pain">Chronic Pain</SelectItem>
                      <SelectItem value="epilepsy">Epilepsy</SelectItem>
                      <SelectItem value="cancer">Cancer</SelectItem>
                      <SelectItem value="anxiety">Anxiety</SelectItem>
                      <SelectItem value="ptsd">PTSD</SelectItem>
                      <SelectItem value="arthritis">Arthritis</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="symptoms">Symptoms</Label>
                  <Textarea
                    id="symptoms"
                    placeholder="Describe your symptoms and how they affect your daily life"
                    value={formData.symptoms}
                    onChange={(e) => handleInputChange('symptoms', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="previous_treatments">Previous Treatments</Label>
                  <Textarea
                    id="previous_treatments"
                    placeholder="List any medications or treatments you have tried"
                    value={formData.previous_treatments}
                    onChange={(e) => handleInputChange('previous_treatments', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="doctor_referral">Doctor Referral (Optional)</Label>
                  <Input
                    id="doctor_referral"
                    placeholder="Name and contact of referring doctor"
                    value={formData.doctor_referral}
                    onChange={(e) => handleInputChange('doctor_referral', e.target.value)}
                  />
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Submitting Application...
                  </>
                ) : (
                  'Submit Application'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}