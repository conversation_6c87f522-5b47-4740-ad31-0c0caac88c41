import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Leaf, Shield, Users, Clock, CheckCircle, Heart } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 space-y-12">
        {/* Hero Section */}
        <div className="text-center space-y-6">
          <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mb-6">
            <Leaf className="w-8 h-8 text-primary-foreground" />
          </div>
          <h1 className="text-4xl font-bold text-foreground">
            About MedCannabis Thailand
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Medican is a digital platform helping Thai residents apply for a legal medical cannabis license with ease. 
            Our system connects patients to licensed doctors for secure, private consultations and fast-track approvals. 
            We're committed to making medical cannabis access simple, safe, and legitimate.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8">
          <Card>
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Heart className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-2xl font-bold">Our Mission</h3>
                <p className="text-muted-foreground">
                  To provide safe, legal, and accessible medical cannabis solutions for Thai patients, 
                  ensuring compliance with local regulations while prioritizing patient care and wellbeing.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-2xl font-bold">Our Vision</h3>
                <p className="text-muted-foreground">
                  To become Thailand's leading digital platform for medical cannabis access, 
                  bridging the gap between patients and licensed healthcare providers through technology.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Key Features */}
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Why Choose MedCannabis Thailand?
            </h2>
            <p className="text-muted-foreground">
              We make medical cannabis access simple, secure, and compliant with Thai law
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6 text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">100% Legal & Compliant</h3>
                <p className="text-muted-foreground">
                  All processes follow Thai medical cannabis regulations and are fully government-approved.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Licensed Doctors</h3>
                <p className="text-muted-foreground">
                  Connect with verified medical professionals specialized in cannabis treatment.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Clock className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Fast Processing</h3>
                <p className="text-muted-foreground">
                  Streamlined application process with quick approval times and digital documentation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Process Steps */}
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              How It Works
            </h2>
            <p className="text-muted-foreground">
              Simple steps to get your medical cannabis license
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            {[
              {
                step: "1",
                title: "Create Account",
                description: "Register with your personal information and medical history"
              },
              {
                step: "2", 
                title: "Doctor Consultation",
                description: "Book an appointment with a licensed medical professional"
              },
              {
                step: "3",
                title: "Medical Assessment",
                description: "Complete your consultation and receive medical recommendation"
              },
              {
                step: "4",
                title: "Get Your License",
                description: "Receive your digital medical cannabis license and start treatment"
              }
            ].map((item, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center space-y-4">
                  <div className="mx-auto w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center">
                    <span className="text-xl font-bold text-primary-foreground">{item.step}</span>
                  </div>
                  <h3 className="text-lg font-semibold">{item.title}</h3>
                  <p className="text-muted-foreground text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="bg-muted/50 rounded-2xl p-8">
          <div className="text-center space-y-6">
            <h2 className="text-3xl font-bold text-foreground">
              Trusted by Patients Across Thailand
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">500+</div>
                <div className="text-muted-foreground">Licensed Doctors</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">10,000+</div>
                <div className="text-muted-foreground">Patients Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">98%</div>
                <div className="text-muted-foreground">Approval Rate</div>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance & Safety */}
        <Card>
          <CardContent className="p-8">
            <div className="text-center space-y-6">
              <div className="flex justify-center gap-4">
                <Badge variant="secondary" className="px-4 py-2">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Government Approved
                </Badge>
                <Badge variant="secondary" className="px-4 py-2">
                  <Shield className="w-4 h-4 mr-2" />
                  HIPAA Compliant
                </Badge>
                <Badge variant="secondary" className="px-4 py-2">
                  <Leaf className="w-4 h-4 mr-2" />
                  Medical Grade
                </Badge>
              </div>
              <h3 className="text-2xl font-bold">Safety & Compliance First</h3>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Your privacy and safety are our top priorities. All medical information is encrypted and stored securely, 
                following international healthcare data protection standards. Our platform is regularly audited to ensure 
                compliance with Thai medical cannabis regulations.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}