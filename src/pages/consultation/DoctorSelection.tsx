import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Stethoscope, Calendar, Star, MapPin } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { DoctorWithProfile } from '@/types/database';

export default function DoctorSelection() {
  const [doctors, setDoctors] = useState<DoctorWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctors = async () => {
    try {
      const { data, error } = await supabase
        .from('doctors')
        .select(`
          *,
          profiles!user_id(full_name, email, phone)
        `)
        .eq('is_verified', true)
        .eq('is_available', true)
        .not('calendly_link', 'is', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const doctorsWithProfile = data?.map(doctor => ({
        ...doctor,
        profiles: Array.isArray(doctor.profiles) ? doctor.profiles[0] : doctor.profiles
      })) || []
      
      setDoctors(doctorsWithProfile);
    } catch (error: any) {
      console.error('Error fetching doctors:', error);
      toast({
        title: "Error",
        description: "Failed to load available doctors",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBookConsultation = (doctor: DoctorWithProfile) => {
    if (doctor.calendly_link) {
      window.open(doctor.calendly_link, '_blank');
      toast({
        title: "Booking Opened",
        description: `Opening booking page for Dr. ${doctor.profiles?.full_name}`,
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center">
          <div className="text-lg">Loading available doctors...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Select a Doctor</h1>
        <p className="text-muted-foreground">
          Choose from our available verified doctors for consultation
        </p>
      </div>

      {doctors.length === 0 ? (
        <Alert>
          <AlertDescription>
            No doctors are currently available for consultation. Please check back later.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {doctors.map((doctor) => (
            <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Stethoscope className="h-5 w-5" />
                  Dr. {doctor.profiles?.full_name || 'Unknown Doctor'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Badge variant="secondary">{doctor.specialization}</Badge>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground">Experience</p>
                  <p className="font-medium">{doctor.years_of_experience} years</p>
                </div>

                {doctor.consultation_fee && (
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">${doctor.consultation_fee}</span>
                  </div>
                )}

                {doctor.clinic_address && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span className="truncate">{doctor.clinic_address}</span>
                  </div>
                )}

                {doctor.bio && (
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {doctor.bio}
                  </p>
                )}

                <Button 
                  className="w-full" 
                  onClick={() => handleBookConsultation(doctor)}
                  disabled={!doctor.calendly_link}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Consultation
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}