import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { signUp } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Leaf, Upload, FileText, CheckCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface DocumentUpload {
  file: File | null;
  uploaded: boolean;
  url?: string;
}

export default function DoctorSignUp() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    full_name: '',
    phone: '',
    medical_license_number: '',
    specialization: '',
    years_of_experience: '',
    clinic_address: '',
    bio: ''
  });
  
  const [documents, setDocuments] = useState({
    medical_license: { file: null, uploaded: false } as DocumentUpload,
    national_id: { file: null, uploaded: false } as DocumentUpload
  });
  
  const [loading, setLoading] = useState(false);
  const [uploadingDocs, setUploadingDocs] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (docType: 'medical_license' | 'national_id', file: File) => {
    setDocuments(prev => ({
      ...prev,
      [docType]: { file, uploaded: false }
    }));
  };

  const uploadDocument = async (docType: 'medical_license' | 'national_id') => {
    const doc = documents[docType];
    if (!doc.file) return null;

    setUploadingDocs(true);
    try {
      const fileExt = doc.file.name.split('.').pop();
      const fileName = `${Date.now()}_${docType}.${fileExt}`;
      const filePath = `doctor-documents/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('documents')
        .upload(filePath, doc.file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(filePath);

      setDocuments(prev => ({
        ...prev,
        [docType]: { ...prev[docType], uploaded: true, url: publicUrl }
      }));

      return publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    } finally {
      setUploadingDocs(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      setLoading(false);
      return;
    }

    if (!documents.medical_license.file || !documents.national_id.file) {
      setError('Please upload both medical license and national ID documents');
      setLoading(false);
      return;
    }

    try {
      // Upload documents first
      const medicalLicenseUrl = await uploadDocument('medical_license');
      const nationalIdUrl = await uploadDocument('national_id');

      if (!medicalLicenseUrl || !nationalIdUrl) {
        throw new Error('Failed to upload documents');
      }

      // Create user account
      const { error: signUpError } = await signUp(formData.email, formData.password, {
        full_name: formData.full_name,
        phone: formData.phone,
        role: 'doctor'
      });

      if (signUpError) throw signUpError;

      // Get user session to create doctor profile
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        // Create doctor record
        const { error: doctorError } = await supabase
          .from('doctors')
          .insert({
            user_id: user.id,
            medical_license_number: formData.medical_license_number,
            specialization: formData.specialization,
            years_of_experience: parseInt(formData.years_of_experience),
            bio: formData.bio,
            clinic_address: formData.clinic_address,
            status: 'pending_review'
          });

        if (doctorError) {
          console.error('Doctor profile creation error:', doctorError);
        }
      }

      toast({
        title: "Doctor application submitted!",
        description: "Your application is under review. We'll contact you within 48 hours to schedule an interview.",
      });
      
      navigate('/auth/signin');
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* Logo */}
        <div className="text-center">
          <div className="mx-auto w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center mb-4">
            <Leaf className="w-7 h-7 text-primary-foreground" />
          </div>
          <h1 className="text-2xl font-bold text-foreground">Doctor Registration</h1>
          <p className="text-muted-foreground">Join our network of licensed medical professionals</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Doctor Application</CardTitle>
            <CardDescription>
              Complete your profile and upload required documents to join our medical network
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="full_name">Full Name *</Label>
                    <Input
                      id="full_name"
                      type="text"
                      placeholder="Dr. John Smith"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+66 XX XXX XXXX"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              {/* Professional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Professional Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="medical_license_number">Medical License Number *</Label>
                    <Input
                      id="medical_license_number"
                      type="text"
                      placeholder="ML123456789"
                      value={formData.medical_license_number}
                      onChange={(e) => handleInputChange('medical_license_number', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="years_of_experience">Years of Experience *</Label>
                    <Input
                      id="years_of_experience"
                      type="number"
                      placeholder="5"
                      value={formData.years_of_experience}
                      onChange={(e) => handleInputChange('years_of_experience', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="specialization">Specialization *</Label>
                  <Input
                    id="specialization"
                    type="text"
                    placeholder="Internal Medicine, Pain Management, etc."
                    value={formData.specialization}
                    onChange={(e) => handleInputChange('specialization', e.target.value)}
                    required
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clinic_address">Clinic/Hospital Address *</Label>
                  <Textarea
                    id="clinic_address"
                    placeholder="123 Hospital Street, Bangkok, Thailand"
                    value={formData.clinic_address}
                    onChange={(e) => handleInputChange('clinic_address', e.target.value)}
                    required
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Professional Bio</Label>
                  <Textarea
                    id="bio"
                    placeholder="Brief description of your medical background and experience..."
                    value={formData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    disabled={loading}
                  />
                </div>
              </div>

              {/* Document Upload */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Required Documents</h3>
                
                {/* Medical License Upload */}
                <div className="space-y-2">
                  <Label>Medical License Document *</Label>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                    <div className="text-center">
                      {documents.medical_license.uploaded ? (
                        <div className="flex items-center justify-center space-x-2 text-green-600">
                          <CheckCircle className="w-5 h-5" />
                          <span>Medical license uploaded successfully</span>
                        </div>
                      ) : (
                        <>
                          <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                          <div className="mt-4">
                            <label htmlFor="medical_license" className="cursor-pointer">
                              <span className="mt-2 block text-sm font-medium text-foreground">
                                Upload Medical License
                              </span>
                              <span className="mt-1 block text-xs text-muted-foreground">
                                PDF, JPG, PNG up to 10MB
                              </span>
                            </label>
                            <Input
                              id="medical_license"
                              type="file"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={(e) => e.target.files?.[0] && handleFileChange('medical_license', e.target.files[0])}
                              className="hidden"
                              disabled={loading || uploadingDocs}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* National ID Upload */}
                <div className="space-y-2">
                  <Label>National ID Document *</Label>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                    <div className="text-center">
                      {documents.national_id.uploaded ? (
                        <div className="flex items-center justify-center space-x-2 text-green-600">
                          <CheckCircle className="w-5 h-5" />
                          <span>National ID uploaded successfully</span>
                        </div>
                      ) : (
                        <>
                          <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                          <div className="mt-4">
                            <label htmlFor="national_id" className="cursor-pointer">
                              <span className="mt-2 block text-sm font-medium text-foreground">
                                Upload National ID
                              </span>
                              <span className="mt-1 block text-xs text-muted-foreground">
                                PDF, JPG, PNG up to 10MB
                              </span>
                            </label>
                            <Input
                              id="national_id"
                              type="file"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={(e) => e.target.files?.[0] && handleFileChange('national_id', e.target.files[0])}
                              className="hidden"
                              disabled={loading || uploadingDocs}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Password Setup */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Account Security</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Create a secure password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Confirm your password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={loading || uploadingDocs}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {uploadingDocs ? 'Uploading Documents...' : 'Submit Doctor Application'}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm">
              <p className="text-muted-foreground">
                Already have an account?{' '}
                <Link to="/auth/signin" className="text-primary hover:underline">
                  Sign in
                </Link>
              </p>
              <p className="text-muted-foreground mt-2">
                Want to apply as a patient?{' '}
                <Link to="/auth/signup" className="text-primary hover:underline">
                  Patient registration
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}