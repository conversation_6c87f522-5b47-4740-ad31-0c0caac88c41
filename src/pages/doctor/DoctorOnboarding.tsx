import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, ExternalLink, CheckCircle, Clock, AlertCircle, UserPlus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import DoctorProfileCreation from '@/components/doctor/DoctorProfileCreation';

interface DoctorProfile {
  id: string;
  status: string | null;
  calendly_link: string | null;
  interview_scheduled_at: string | null;
  consultation_fee: number | null;
  is_active: boolean | null;
}

export default function DoctorOnboarding() {
  const { user } = useAuth();
  const [doctorProfile, setDoctorProfile] = useState<DoctorProfile | null>(null);
  const [calendlyLink, setCalendlyLink] = useState('');
  const [consultationFee, setConsultationFee] = useState('');
  const [availabilityNotes, setAvailabilityNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchDoctorProfile();
    }
  }, [user]);

  const fetchDoctorProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('doctors')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      const profileWithIsActive = data ? { ...data, is_active: data.is_available } : null;
      setDoctorProfile(profileWithIsActive);
      if (data?.calendly_link) {
        setCalendlyLink(data.calendly_link);
      }
      if (data?.consultation_fee) {
        setConsultationFee(data.consultation_fee.toString());
      }
    } catch (error) {
      console.error('Error fetching doctor profile:', error);
      toast({
        title: "Error",
        description: "Failed to load doctor profile",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateCalendlySettings = async () => {
    if (!doctorProfile) return;

    setUpdating(true);
    try {
      const updates: any = {
        calendly_link: calendlyLink,
        consultation_fee: consultationFee ? parseInt(consultationFee) : null,
        is_active: !!calendlyLink && !!consultationFee
      };

      const { error } = await supabase
        .from('doctors')
        .update(updates)
        .eq('id', doctorProfile.id);

      if (error) throw error;

      toast({
        title: "Settings Updated",
        description: "Your Calendly settings have been saved successfully",
      });

      // Refresh profile
      fetchDoctorProfile();
    } catch (error) {
      console.error('Error updating settings:', error);
      toast({
        title: "Error",
        description: "Failed to update settings",
        variant: "destructive"
      });
    } finally {
      setUpdating(false);
    }
  };

  const validateCalendlyUrl = (url: string) => {
    return url.includes('calendly.com/') && url.startsWith('https://');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!doctorProfile) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Doctor Onboarding</h1>
            <p className="text-muted-foreground">Complete your profile setup to start accepting consultations</p>
          </div>
          <Badge variant="secondary">
            <UserPlus className="w-3 h-3 mr-1" />
            New Application
          </Badge>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Complete your doctor profile to start the application process.
          </AlertDescription>
        </Alert>

        <DoctorProfileCreation 
          userId={user?.id || ''} 
          onProfileCreated={fetchDoctorProfile}
        />
      </div>
    );
  }

  const getStatusBadge = () => {
    switch (doctorProfile.status) {
      case 'pending_review':
        return <Badge variant="secondary">Pending Review</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const isApproved = doctorProfile.status === 'approved';

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Doctor Onboarding</h1>
          <p className="text-muted-foreground">Complete your profile setup to start accepting consultations</p>
        </div>
        {getStatusBadge()}
      </div>

      {/* Application Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Application Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Application Review</h3>
                <p className="text-sm text-muted-foreground">
                  Admin review of your credentials and documents
                </p>
              </div>
              {getStatusBadge()}
            </div>

            {doctorProfile.interview_scheduled_at && (
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Interview Scheduled</h3>
                  <p className="text-sm text-muted-foreground">
                    {new Date(doctorProfile.interview_scheduled_at).toLocaleString()}
                  </p>
                </div>
                <Badge variant="outline" className="text-blue-600">
                  <Clock className="w-3 h-3 mr-1" />
                  Scheduled
                </Badge>
              </div>
            )}

            {!isApproved && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Your application is currently under review. You'll be able to set up your availability once approved.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Calendly Setup - Only show if approved */}
      {isApproved && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Consultation Availability Setup
            </CardTitle>
            <CardDescription>
              Connect your Calendly account to manage consultation bookings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                You need a Calendly account to manage consultation bookings. If you don't have one, 
                <a 
                  href="https://calendly.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline ml-1"
                >
                  create one here <ExternalLink className="w-3 h-3 inline" />
                </a>
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="calendly_link">Your Calendly Booking Link *</Label>
                <Input
                  id="calendly_link"
                  placeholder="https://calendly.com/your-username/consultation"
                  value={calendlyLink}
                  onChange={(e) => setCalendlyLink(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Copy your Calendly booking link from your Calendly dashboard
                </p>
                {calendlyLink && !validateCalendlyUrl(calendlyLink) && (
                  <p className="text-sm text-destructive">
                    Please enter a valid Calendly URL (https://calendly.com/...)
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="consultation_fee">Consultation Fee (THB) *</Label>
                <Input
                  id="consultation_fee"
                  type="number"
                  placeholder="1500"
                  value={consultationFee}
                  onChange={(e) => setConsultationFee(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Set your consultation fee in Thai Baht
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="availability_notes">Availability Notes (Optional)</Label>
                <Textarea
                  id="availability_notes"
                  placeholder="Additional information about your availability..."
                  value={availabilityNotes}
                  onChange={(e) => setAvailabilityNotes(e.target.value)}
                />
              </div>

              <Button 
                onClick={updateCalendlySettings} 
                disabled={updating || !calendlyLink || !consultationFee || !validateCalendlyUrl(calendlyLink)}
                className="w-full"
              >
                {updating ? 'Updating...' : 'Save Availability Settings'}
              </Button>

              {doctorProfile.calendly_link && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Preview Your Booking Page</h4>
                  <Button
                    variant="outline"
                    onClick={() => window.open(doctorProfile.calendly_link!, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Your Calendly Page
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Status */}
      {isApproved && doctorProfile.calendly_link && (
        <Card>
          <CardHeader>
            <CardTitle>Profile Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${doctorProfile.is_active ? 'bg-green-500' : 'bg-gray-400'}`} />
                <span className="font-medium">
                  {doctorProfile.is_active ? 'Active - Accepting Consultations' : 'Inactive'}
                </span>
              </div>
              {doctorProfile.is_active && (
                <Badge variant="outline" className="text-green-600">
                  Available for Booking
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}