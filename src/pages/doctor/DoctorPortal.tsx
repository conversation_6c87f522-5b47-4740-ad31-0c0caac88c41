import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  Calendar, 
  FileText, 
  DollarSign, 
  Users, 
  Settings,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart3
} from 'lucide-react';
import { Loader2 } from 'lucide-react';

export default function DoctorPortal() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Please sign in to access the doctor portal.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (user.profile?.role !== 'doctor') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Access denied. This portal is only available for doctors.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Mock status data - would come from doctor profile in real implementation
  const doctorStatus = {
    profileComplete: true,
    approved: true,
    active: false,
    pendingConsultations: 3,
    totalConsultations: 12,
    monthlyEarnings: 2400
  };

  const getStatusBadge = () => {
    if (!doctorStatus.approved) {
      return <Badge variant="secondary" className="gap-1"><Clock className="w-3 h-3" />Pending Approval</Badge>;
    }
    if (!doctorStatus.active) {
      return <Badge variant="outline" className="gap-1"><AlertCircle className="w-3 h-3" />Setup Required</Badge>;
    }
    return <Badge variant="default" className="gap-1"><CheckCircle className="w-3 h-3" />Active</Badge>;
  };

  const navigationCards = [
    {
      title: "Profile Management",
      description: "View and edit your professional profile",
      icon: User,
      action: () => navigate('/profile'),
      status: doctorStatus.profileComplete ? "Complete" : "Incomplete"
    },
    {
      title: "Dashboard Analytics",
      description: "View detailed statistics and performance",
      icon: BarChart3,
      action: () => navigate('/doctor/dashboard'),
      status: "Available"
    },
    {
      title: "Settings & Setup",
      description: "Configure Calendly link and consultation fees",
      icon: Settings,
      action: () => navigate('/doctor/onboarding'),
      status: doctorStatus.active ? "Complete" : "Required"
    },
    {
      title: "Consultations",
      description: "Manage your consultation requests",
      icon: Calendar,
      action: () => navigate('/doctor/consultations'),
      status: `${doctorStatus.pendingConsultations} pending`
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Welcome Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Welcome, Dr. {user.profile?.full_name || 'Doctor'}
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage your practice and consultations from your portal
            </p>
          </div>
          <div className="flex items-center gap-3">
            {getStatusBadge()}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Consultations</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{doctorStatus.totalConsultations}</div>
            <p className="text-xs text-muted-foreground">
              {doctorStatus.pendingConsultations} pending review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Earnings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${doctorStatus.monthlyEarnings}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Patients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">
              3 new this week
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Action Items */}
      {!doctorStatus.active && (
        <Alert className="mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Complete your setup to start accepting consultations. 
            <Button variant="link" className="p-0 h-auto font-medium ml-1" onClick={() => navigate('/doctor/onboarding')}>
              Complete setup now
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Navigation Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {navigationCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow" onClick={card.action}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{card.title}</CardTitle>
                      <CardDescription>{card.description}</CardDescription>
                    </div>
                  </div>
                  <Badge variant={card.status === "Complete" || card.status === "Available" ? "default" : "secondary"}>
                    {card.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <Button variant="outline" size="sm" className="w-full">
                  Open {card.title}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}