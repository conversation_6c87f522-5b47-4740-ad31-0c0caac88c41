import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, Clock, User, Mail } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface ConsultationRequest {
  id: string;
  patient_name: string;
  patient_email: string;
  requested_at: string;
  status: string;
  notes?: string;
}

export default function DoctorConsultations() {
  const { user } = useAuth();
  const [consultations, setConsultations] = useState<ConsultationRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchConsultations();
    }
  }, [user]);

  const fetchConsultations = async () => {
    try {
      // For now, we'll show a placeholder since we don't have a consultations table
      // In a real implementation, this would fetch from a consultations table
      setConsultations([]);
    } catch (error: any) {
      console.error('Error fetching consultations:', error);
      toast({
        title: "Error",
        description: "Failed to load consultations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Alert>
        <AlertDescription>
          Please sign in to view your consultations.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center">
          <div className="text-lg">Loading consultations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Consultations</h1>
        <p className="text-muted-foreground">
          Manage your upcoming and past consultations
        </p>
      </div>

      {consultations.length === 0 ? (
        <Alert>
          <AlertDescription>
            No consultations scheduled yet. Patients can book appointments through your Calendly link.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid gap-6">
          {consultations.map((consultation) => (
            <Card key={consultation.id}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {consultation.patient_name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  {consultation.patient_email}
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  Requested: {new Date(consultation.requested_at).toLocaleDateString()}
                </div>

                <div>
                  <Badge variant={
                    consultation.status === 'completed' ? 'default' :
                    consultation.status === 'scheduled' ? 'secondary' : 'outline'
                  }>
                    {consultation.status}
                  </Badge>
                </div>

                {consultation.notes && (
                  <div>
                    <p className="text-sm text-muted-foreground">Notes</p>
                    <p className="text-sm">{consultation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
