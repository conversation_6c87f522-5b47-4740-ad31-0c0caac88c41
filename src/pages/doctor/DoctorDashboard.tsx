import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  FileText, 
  Settings, 
  LogOut, 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle,
  DollarSign,
  Star,
  Activity
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface DashboardStats {
  totalConsultations: number;
  pendingConsultations: number;
  approvedConsultations: number;
  rejectedConsultations: number;
  totalEarnings: number;
  activePatients: number;
}

export default function DoctorDashboard() {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalConsultations: 0,
    pendingConsultations: 0,
    approvedConsultations: 0,
    rejectedConsultations: 0,
    totalEarnings: 0,
    activePatients: 0
  });
  const [loading, setLoading] = useState(true);

  const handleSignOut = async () => {
    await signOut();
    window.location.href = '/';
  };

  useEffect(() => {
    if (user?.id) {
      fetchDashboardStats();
    }
  }, [user?.id]);

  const fetchDashboardStats = async () => {
    try {
      // For now, show placeholder stats since we don't have a consultations table
      // In a real implementation, this would fetch from a consultations table
      setStats({
        totalConsultations: 0,
        pendingConsultations: 0,
        approvedConsultations: 0,
        rejectedConsultations: 0,
        totalEarnings: 0,
        activePatients: 0
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Please sign in to access your dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Dr. {user.profile?.full_name || user.email}
            </h1>
            <p className="text-muted-foreground">
              Doctor Dashboard - Manage your consultations and patients
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              Doctor
            </Badge>
            <Button variant="outline" onClick={handleSignOut}>
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Consultations</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '-' : stats.totalConsultations}</div>
              <p className="text-xs text-muted-foreground">
                All time consultations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{loading ? '-' : stats.pendingConsultations}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting your review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Patients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '-' : stats.activePatients}</div>
              <p className="text-xs text-muted-foreground">
                Unique patients served
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${loading ? '-' : stats.totalEarnings}</div>
              <p className="text-xs text-muted-foreground">
                From approved consultations
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Profile Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Manage your doctor profile
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm font-medium">Email</p>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Role</p>
                <p className="text-sm text-muted-foreground">Doctor</p>
              </div>
              {user.profile?.phone && (
                <div>
                  <p className="text-sm font-medium">Phone</p>
                  <p className="text-sm text-muted-foreground">{user.profile.phone}</p>
                </div>
              )}
              <Button variant="outline" size="sm" className="w-full mt-4" onClick={() => navigate('/profile/edit')}>
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          {/* Consultation Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Consultation Status
              </CardTitle>
              <CardDescription>
                Review breakdown of consultations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm">Approved</span>
                </div>
                <Badge variant="default">{stats.approvedConsultations}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-orange-600" />
                  <span className="text-sm">Pending</span>
                </div>
                <Badge variant="secondary">{stats.pendingConsultations}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <XCircle className="w-4 h-4 text-red-600" />
                  <span className="text-sm">Rejected</span>
                </div>
                <Badge variant="outline">{stats.rejectedConsultations}</Badge>
              </div>
              <Button variant="outline" className="w-full mt-4" onClick={() => navigate('/doctor/consultations')}>
                Review Consultations
              </Button>
            </CardContent>
          </Card>

          {/* Schedule Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Schedule Management
              </CardTitle>
              <CardDescription>
                Manage your availability and bookings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground text-center">
                  Set your consultation availability
                </p>
                <Button variant="outline" className="w-full" onClick={() => navigate('/doctor/onboarding')}>
                  Manage Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts for doctors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-3">
              <Button variant="outline" className="justify-start" onClick={() => navigate('/doctor/consultations')}>
                <FileText className="w-4 h-4 mr-2" />
                Review Consultations
              </Button>
              <Button variant="outline" className="justify-start" onClick={() => navigate('/doctor/onboarding')}>
                <Calendar className="w-4 h-4 mr-2" />
                Set Availability
              </Button>
              <Button variant="outline" className="justify-start" onClick={() => navigate('/profile/edit')}>
                <Settings className="w-4 h-4 mr-2" />
                Update Profile
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}