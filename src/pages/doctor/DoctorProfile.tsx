import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ProfilePhotoUpload } from '@/components/profile/ProfilePhotoUpload'
import { 
  Edit3, 
  MapPin, 
  Calendar, 
  Stethoscope, 
  Star, 
  Award, 
  Clock,
  Phone,
  Mail,
  Loader2
} from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface DoctorData {
  id: string
  user_id: string
  bio?: string
  specialization: string
  years_of_experience: number
  consultation_fee?: number
  calendly_link?: string
  clinic_address?: string
  status: string
  is_verified: boolean
  is_available: boolean
  created_at: string
  profile?: {
    full_name: string
    email: string
    phone?: string
  }
  avatar_url?: string
  cover_url?: string
}

export default function DoctorProfile() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [doctorData, setDoctorData] = useState<DoctorData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchDoctorProfile()
    }
  }, [user])

  const fetchDoctorProfile = async () => {
    if (!user) return

    try {
      // Fetch doctor data
      const { data: doctorData, error: doctorError } = await supabase
        .from('doctors')
        .select(`
          *,
          profiles!user_id(full_name, email, phone)
        `)
        .eq('user_id', user.id)
        .maybeSingle()

      if (doctorError) throw doctorError

      if (doctorData) {
        const profile = Array.isArray(doctorData.profiles) ? doctorData.profiles[0] : doctorData.profiles
        
        // Get profile photos
        const { data: avatarData } = supabase.storage
          .from('profile-photos')
          .getPublicUrl(`${user.id}/avatar.jpg`)
        
        const { data: coverData } = supabase.storage
          .from('profile-photos') 
          .getPublicUrl(`${user.id}/cover.jpg`)

        setDoctorData({
          ...doctorData,
          profile,
          avatar_url: avatarData.publicUrl,
          cover_url: coverData.publicUrl
        })
      }
    } catch (error: any) {
      console.error('Error fetching doctor profile:', error)
      toast({
        title: "Error loading profile",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePhotoUpdate = (url: string, type: 'avatar' | 'cover') => {
    if (doctorData) {
      setDoctorData({
        ...doctorData,
        [type === 'avatar' ? 'avatar_url' : 'cover_url']: url
      })
    }
  }

  const getStatusBadge = (status: string, isVerified: boolean) => {
    if (isVerified) {
      return <Badge variant="default" className="bg-green-500">Verified</Badge>
    }
    
    switch (status) {
      case 'approved':
        return <Badge variant="default">Approved</Badge>
      case 'pending_review':
        return <Badge variant="secondary">Pending Review</Badge>
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (!doctorData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert>
          <AlertDescription>
            Doctor profile not found. Please complete your doctor registration.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto max-w-4xl">
        {/* Cover Photo Section */}
        <div className="relative">
          <ProfilePhotoUpload
            userId={user?.id || ''}
            currentPhotoUrl={doctorData.cover_url}
            onPhotoUpdate={(url) => handlePhotoUpdate(url, 'cover')}
            type="cover"
          />
          
          {/* Profile Header */}
          <div className="absolute -bottom-16 left-8 flex items-end gap-6">
            <ProfilePhotoUpload
              userId={user?.id || ''}
              currentPhotoUrl={doctorData.avatar_url}
              onPhotoUpdate={(url) => handlePhotoUpdate(url, 'avatar')}
              size="lg"
              type="avatar"
            />
            
            <div className="pb-4">
              <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
                Dr. {doctorData.profile?.full_name || 'Unknown'}
              </h1>
              <div className="flex items-center gap-2 mb-2">
                <Stethoscope className="w-4 h-4 text-white" />
                <span className="text-white drop-shadow">{doctorData.specialization}</span>
              </div>
              {getStatusBadge(doctorData.status, doctorData.is_verified)}
            </div>
          </div>

          {/* Edit Button */}
          <div className="absolute top-4 right-4">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => navigate('/profile/edit')}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="pt-20 px-6 pb-6 space-y-6">
          {/* Professional Information */}
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2 space-y-6">
              {/* About */}
              <Card>
                <CardHeader>
                  <CardTitle>About</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {doctorData.bio || "No bio available yet. Click 'Edit Profile' to add your professional background and experience."}
                  </p>
                </CardContent>
              </Card>

              {/* Professional Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Professional Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Award className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Specialization</p>
                      <p className="text-muted-foreground">{doctorData.specialization}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Experience</p>
                      <p className="text-muted-foreground">{doctorData.years_of_experience} years</p>
                    </div>
                  </div>

                  {doctorData.clinic_address && (
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Clinic Address</p>
                        <p className="text-muted-foreground">{doctorData.clinic_address}</p>
                      </div>
                    </div>
                  )}

                  {doctorData.consultation_fee && (
                    <div className="flex items-center gap-3">
                      <Star className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Consultation Fee</p>
                        <p className="text-muted-foreground">${doctorData.consultation_fee}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{doctorData.profile?.email}</span>
                  </div>
                  
                  {doctorData.profile?.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{doctorData.profile.phone}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Availability */}
              <Card>
                <CardHeader>
                  <CardTitle>Availability</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2 mb-4">
                    <div className={`w-3 h-3 rounded-full ${doctorData.is_available ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm font-medium">
                      {doctorData.is_available ? 'Available for consultations' : 'Currently unavailable'}
                    </span>
                  </div>
                  
                  {doctorData.calendly_link && doctorData.is_available && (
                    <Button 
                      className="w-full" 
                      onClick={() => window.open(doctorData.calendly_link, '_blank')}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      Book Consultation
                    </Button>
                  )}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/doctor/onboarding')}
                  >
                    Setup Availability
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/doctor/consultations')}
                  >
                    View Consultations
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}