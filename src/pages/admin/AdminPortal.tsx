import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  UserCheck, 
  Calendar,
  FileText,
  Settings,
  BarChart3,
  Shield,
  MessageSquare,
  AlertTriangle,
  ChevronRight
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface AdminStats {
  totalUsers: number;
  totalDoctors: number;
  totalCustomers: number;
  pendingDoctorReviews: number;
  scheduledInterviews: number;
  activeConsultations: number;
}

export default function AdminPortal() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalDoctors: 0,
    totalCustomers: 0,
    pendingDoctorReviews: 0,
    scheduledInterviews: 0,
    activeConsultations: 0,
  });
  const [loadingStats, setLoadingStats] = useState(true);

  useEffect(() => {
    if (!loading && (!user || user.profile?.role !== 'admin')) {
      navigate('/auth/signin');
      return;
    }
    
    if (user?.profile?.role === 'admin') {
      fetchStats();
    }
  }, [user, loading, navigate]);

  const fetchStats = async () => {
    try {
      // Get total users by role
      const { data: profiles } = await supabase
        .from('profiles')
        .select('role');

      // Get pending doctor reviews
      const { data: pendingDoctors } = await supabase
        .from('doctors')
        .select('id')
        .eq('status', 'pending_review');

      // Get scheduled interviews
      const { data: scheduledInterviews } = await supabase
        .from('doctors')
        .select('id')
        .not('interview_scheduled_at', 'is', null);

      if (profiles) {
        const totalUsers = profiles.length;
        const totalDoctors = profiles.filter(p => p.role === 'doctor').length;
        const totalCustomers = profiles.filter(p => p.role === 'customer').length;

        setStats({
          totalUsers,
          totalDoctors,
          totalCustomers,
          pendingDoctorReviews: pendingDoctors?.length || 0,
          scheduledInterviews: scheduledInterviews?.length || 0,
          activeConsultations: 0, // TODO: Implement when consultations table exists
        });
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  if (!user || user.profile?.role !== 'admin') {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Access denied. Admin privileges required.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const adminSections = [
    {
      title: 'Doctor Management',
      description: 'Review and manage doctor applications',
      icon: UserCheck,
      items: [
        {
          title: 'Doctor Review',
          description: 'Review pending doctor applications',
          path: '/admin/doctor-review',
          badge: stats.pendingDoctorReviews > 0 ? stats.pendingDoctorReviews : null,
          badgeVariant: 'destructive' as const
        },
        {
          title: 'Interview Scheduling',
          description: 'Schedule and manage doctor interviews',
          path: '/admin/interview-scheduling',
          badge: stats.scheduledInterviews > 0 ? stats.scheduledInterviews : null,
          badgeVariant: 'secondary' as const
        },
        {
          title: 'All Doctors',
          description: 'View and manage all registered doctors',
          path: '/admin/doctors',
          badge: stats.totalDoctors,
          badgeVariant: 'outline' as const
        }
      ]
    },
    {
      title: 'User Management',
      description: 'Manage customers and user accounts',
      icon: Users,
      items: [
        {
          title: 'All Customers',
          description: 'View and manage customer accounts',
          path: '/admin/customers',
          badge: stats.totalCustomers,
          badgeVariant: 'outline' as const
        },
        {
          title: 'User Analytics',
          description: 'View user registration and activity analytics',
          path: '/admin/analytics',
          badge: null,
          badgeVariant: 'outline' as const
        }
      ]
    },
    {
      title: 'Platform Management',
      description: 'System settings and configuration',
      icon: Settings,
      items: [
        {
          title: 'System Settings',
          description: 'Configure platform settings and preferences',
          path: '/admin/settings',
          badge: null,
          badgeVariant: 'outline' as const
        },
        {
          title: 'Document Management',
          description: 'View and manage uploaded documents',
          path: '/admin/documents',
          badge: null,
          badgeVariant: 'outline' as const
        },
        {
          title: 'Audit Logs',
          description: 'View system activity and admin actions',
          path: '/admin/audit',
          badge: null,
          badgeVariant: 'outline' as const
        }
      ]
    },
    {
      title: 'Communication',
      description: 'Messaging and notifications',
      icon: MessageSquare,
      items: [
        {
          title: 'Send Notifications',
          description: 'Send messages to users and doctors',
          path: '/admin/notifications',
          badge: null,
          badgeVariant: 'outline' as const
        },
        {
          title: 'Message Templates',
          description: 'Manage email and notification templates',
          path: '/admin/templates',
          badge: null,
          badgeVariant: 'outline' as const
        }
      ]
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Admin Portal</h1>
        <p className="text-muted-foreground">
          Platform administration and management
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loadingStats ? '...' : stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">All registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Doctors</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loadingStats ? '...' : stats.totalDoctors}</div>
            <p className="text-xs text-muted-foreground">Registered doctors</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loadingStats ? '...' : stats.pendingDoctorReviews}</div>
            <p className="text-xs text-muted-foreground">Awaiting review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Interviews</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loadingStats ? '...' : stats.scheduledInterviews}</div>
            <p className="text-xs text-muted-foreground">Scheduled interviews</p>
          </CardContent>
        </Card>
      </div>

      {/* Admin Sections */}
      <div className="grid gap-6 md:grid-cols-2">
        {adminSections.map((section) => (
          <Card key={section.title}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <section.icon className="h-5 w-5" />
                {section.title}
              </CardTitle>
              <p className="text-sm text-muted-foreground">{section.description}</p>
            </CardHeader>
            <CardContent className="space-y-3">
              {section.items.map((item) => (
                <div key={item.title} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <h4 className="font-medium">{item.title}</h4>
                      {item.badge !== null && (
                        <Badge variant={item.badgeVariant}>{item.badge}</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(item.path)}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}