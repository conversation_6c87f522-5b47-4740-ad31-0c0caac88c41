import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertTriangle, 
  Eye, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Mail,
  Phone,
  Clock
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { DoctorWithProfile } from '@/types/database';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function DoctorReview() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [doctors, setDoctors] = useState<DoctorWithProfile[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(true);
  const [selectedDoctor, setSelectedDoctor] = useState<DoctorWithProfile | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');

  useEffect(() => {
    if (!loading && (!user || user.profile?.role !== 'admin')) {
      navigate('/auth/signin');
      return;
    }
    
    if (user?.profile?.role === 'admin') {
      fetchPendingDoctors();
    }
  }, [user, loading, navigate]);

  const fetchPendingDoctors = async () => {
    try {
      const { data, error } = await supabase
        .from('doctors')
        .select(`
          *,
          profiles(full_name, email, phone)
        `)
        .eq('status', 'pending_review')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const doctorsWithProfile = data?.map(doctor => ({
        ...doctor,
        profiles: Array.isArray(doctor.profiles) ? doctor.profiles[0] : doctor.profiles
      })) || [];
      
      setDoctors(doctorsWithProfile);
    } catch (error: any) {
      console.error('Error fetching pending doctors:', error);
      toast({
        title: "Error",
        description: "Failed to load pending doctor applications",
        variant: "destructive",
      });
    } finally {
      setLoadingDoctors(false);
    }
  };

  const updateDoctorStatus = async (doctorId: string, newStatus: string, notes?: string) => {
    try {
      const updates: any = { 
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      if (newStatus === 'approved') {
        updates.is_verified = true;
      }

      const { error } = await supabase
        .from('doctors')
        .update(updates)
        .eq('id', doctorId);

      if (error) throw error;

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_user_id: user!.id,
          action_type: 'doctor_review',
          target_type: 'doctor',
          target_id: doctorId,
          details: {
            status: newStatus,
            notes: notes || ''
          }
        });

      toast({
        title: "Success",
        description: `Doctor application ${newStatus}`,
      });

      fetchPendingDoctors();
      setSelectedDoctor(null);
      setReviewNotes('');
    } catch (error: any) {
      console.error('Error updating doctor status:', error);
      toast({
        title: "Error",
        description: "Failed to update doctor status",
        variant: "destructive",
      });
    }
  };

  if (loading || loadingDoctors) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  if (!user || user.profile?.role !== 'admin') {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Access denied. Admin privileges required.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Doctor Review</h1>
          <p className="text-muted-foreground">
            Review and approve pending doctor applications ({doctors.length} pending)
          </p>
        </div>
        <Button onClick={() => navigate('/admin')} variant="outline">
          Back to Admin Portal
        </Button>
      </div>

      {doctors.length === 0 ? (
        <Alert>
          <AlertDescription>
            No pending doctor applications requiring review.
          </AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Pending Applications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Doctor Information</TableHead>
                    <TableHead>Specialization</TableHead>
                    <TableHead>Experience</TableHead>
                    <TableHead>Applied</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {doctors.map((doctor) => (
                    <TableRow key={doctor.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {doctor.profiles?.full_name || 'Unknown'}
                          </div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {doctor.profiles?.email}
                          </div>
                          {doctor.profiles?.phone && (
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              {doctor.profiles?.phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{doctor.specialization}</div>
                          <div className="text-sm text-muted-foreground">
                            License: {doctor.medical_license_number}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{doctor.years_of_experience} years</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {new Date(doctor.created_at).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedDoctor(doctor)}
                              >
                                <Eye className="h-4 w-4" />
                                Review
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Review Doctor Application</DialogTitle>
                              </DialogHeader>
                              {selectedDoctor && (
                                <div className="space-y-6">
                                  {/* Doctor Details */}
                                  <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                      <h4 className="font-medium mb-2">Personal Information</h4>
                                      <div className="space-y-2 text-sm">
                                        <div><strong>Name:</strong> {selectedDoctor.profiles?.full_name}</div>
                                        <div><strong>Email:</strong> {selectedDoctor.profiles?.email}</div>
                                        <div><strong>Phone:</strong> {selectedDoctor.profiles?.phone || 'Not provided'}</div>
                                      </div>
                                    </div>
                                    <div>
                                      <h4 className="font-medium mb-2">Professional Information</h4>
                                      <div className="space-y-2 text-sm">
                                        <div><strong>Specialization:</strong> {selectedDoctor.specialization}</div>
                                        <div><strong>Experience:</strong> {selectedDoctor.years_of_experience} years</div>
                                        <div><strong>License:</strong> {selectedDoctor.medical_license_number}</div>
                                        <div><strong>Fee:</strong> ${selectedDoctor.consultation_fee || 'Not set'}</div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Bio */}
                                  {selectedDoctor.bio && (
                                    <div>
                                      <h4 className="font-medium mb-2">Bio</h4>
                                      <p className="text-sm text-muted-foreground">{selectedDoctor.bio}</p>
                                    </div>
                                  )}

                                  {/* Review Notes */}
                                  <div>
                                    <h4 className="font-medium mb-2">Review Notes (Optional)</h4>
                                    <Textarea
                                      placeholder="Add any notes about this review..."
                                      value={reviewNotes}
                                      onChange={(e) => setReviewNotes(e.target.value)}
                                    />
                                  </div>

                                  {/* Actions */}
                                  <div className="flex gap-3 pt-4">
                                    <Button
                                      onClick={() => updateDoctorStatus(selectedDoctor.id, 'approved', reviewNotes)}
                                      className="flex-1"
                                    >
                                      <CheckCircle className="h-4 w-4 mr-2" />
                                      Approve Application
                                    </Button>
                                    <Button
                                      variant="destructive"
                                      onClick={() => updateDoctorStatus(selectedDoctor.id, 'rejected', reviewNotes)}
                                      className="flex-1"
                                    >
                                      <XCircle className="h-4 w-4 mr-2" />
                                      Reject Application
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}